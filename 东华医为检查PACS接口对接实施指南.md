# 东华医为检查PACS接口对接实施指南

## 项目概述

**项目名称：** 中山大学附属口腔医院PACS系统对接  
**系统提供商：** 东华医为科技有限公司  
**对接系统：** 东华医院信息平台系统集成解决方案(检查部分)  
**文档版本：** V1.0  
**编制日期：** 2021年8月1日  

## 一、系统架构信息

### 1.1 基本架构
- **通讯协议：** WebService + XML
- **数据格式：** 标准XML消息
- **认证方式：** 基于WebService的标准认证
- **系统平台：** InterSystems Caché

### 1.2 核心接口规范
- **服务方法名：** `HIPMessageServer`
- **入参1：** `action` (String) - 服务编码
- **入参2：** `message` (String) - XML消息体
- **调用方式：** 同步调用，实时响应

## 二、接口地址信息

### 2.1 生产环境地址
```
WebService地址：
https://**************:1443/csp/hsb/DHC.Published.PUB0001.BS.PUB0001.CLS

WSDL文件地址：
https://**************:1443/csp/hsb/DHC.Published.PUB0001.BS.PUB0001.CLS?WSDL=1
```

### 2.2 服务器信息
- **服务器IP：** **************
- **端口：** 1443 (HTTPS)
- **协议：** HTTPS (安全连接)
- **服务路径：** /csp/hsb/DHC.Published.PUB0001.BS.PUB0001.CLS

## 三、WSDL文件获取详细操作指南

### 3.1 方法一：浏览器直接获取（推荐）

**步骤1：打开浏览器**
```
在地址栏输入：
https://**************:1443/csp/hsb/DHC.Published.PUB0001.BS.PUB0001.CLS?WSDL=1
```

**步骤2：保存WSDL文件**
```
1. 等待页面完全加载显示WSDL内容
2. 右键点击页面空白处
3. 选择"另存为" 或 "Save as"
4. 文件类型选择"网页，仅HTML (.html)"
5. 文件名修改为：DHC_HIS_Interface.wsdl
6. 点击保存
```

**步骤3：验证文件**
```
用文本编辑器打开保存的.wsdl文件，确认包含：
- <?xml version="1.0" encoding="UTF-8"?>
- <definitions xmlns:wsdl="..." ...>
- <service name="..." ...>
- <operation name="HIPMessageServer" ...>
```

### 3.2 方法二：命令行获取

**Windows PowerShell：**
```powershell
# 下载WSDL文件
Invoke-WebRequest -Uri "https://**************:1443/csp/hsb/DHC.Published.PUB0001.BS.PUB0001.CLS?WSDL=1" -OutFile "DHC_HIS_Interface.wsdl" -SkipCertificateCheck
```

**Linux/Mac Terminal：**
```bash
# 使用curl下载
curl -k -o DHC_HIS_Interface.wsdl "https://**************:1443/csp/hsb/DHC.Published.PUB0001.BS.PUB0001.CLS?WSDL=1"

# 或使用wget下载
wget --no-check-certificate -O DHC_HIS_Interface.wsdl "https://**************:1443/csp/hsb/DHC.Published.PUB0001.BS.PUB0001.CLS?WSDL=1"
```

### 3.3 方法三：开发工具导入

**SoapUI导入：**
```
1. 打开SoapUI
2. File → New SOAP Project
3. Project Name: DHC_HIS_Interface
4. Initial WSDL: https://**************:1443/csp/hsb/DHC.Published.PUB0001.BS.PUB0001.CLS?WSDL=1
5. 点击OK自动导入
```

**Postman导入：**
```
1. 打开Postman
2. Import → Link
3. 输入WSDL地址
4. Continue → Import
```

## 四、业务接口服务清单

### 4.1 主要业务服务

| 服务编码 | 服务名称 | 服务方向 | 调用时机 | 重要级别 |
|---------|---------|---------|---------|----------|
| - | 检查申请单信息发送(AddRisAppBill) | HIS→PACS | 住院保存/门诊计费后 | ⭐⭐⭐⭐⭐ |
| - | 医嘱状态变更(UpdateOrdersStatus) | HIS→PACS | 医嘱状态变化时 | ⭐⭐⭐⭐ |
| - | 检查状态变更回传 | PACS→HIS | 检查节点状态变化时 | ⭐⭐⭐⭐⭐ |
| - | 检查危急值接收(ReCriticalValues) | PACS→HIS | 出现危急值时 | ⭐⭐⭐⭐⭐ |
| - | 检查危急值查看状态(RisCriticalValuesCheck) | HIS→PACS | 危急值反馈时 | ⭐⭐⭐⭐ |
| - | 文档注册(RegisterDocument) | PACS→HIS | 报告文档生成后 | ⭐⭐⭐⭐⭐ |

### 4.2 基础字典服务

| 服务名称 | 数据内容 | 更新频率 | 重要级别 |
|---------|---------|---------|----------|
| 医护人员字典(SendManagerDataInfo) | 医生、护士、技师信息 | 按需更新 | ⭐⭐⭐ |
| 科室字典 | 临床科室、医技科室信息 | 按需更新 | ⭐⭐⭐⭐ |
| 病区字典 | 住院病区信息 | 按需更新 | ⭐⭐⭐ |
| 床位字典 | 病床信息 | 按需更新 | ⭐⭐ |
| 医嘱大类字典 | 检查、治疗等大类 | 很少更新 | ⭐⭐⭐ |
| 医嘱子类字典 | 放射、超声等子类 | 很少更新 | ⭐⭐⭐⭐ |
| 医嘱项字典 | 具体检查项目 | 定期更新 | ⭐⭐⭐⭐⭐ |

## 五、检查状态流转图

### 5.1 状态代码定义

| 状态代码 | 状态描述 | 顺序 | 说明 |
|---------|---------|-----|------|
| AP | 申请 | 0 | 医生开立检查申请 |
| BK | 预约 | 1 | 预约检查时间 |
| SC | 登记 | 2 | 患者到达检查科室登记 |
| IM | 已有图像 | 3 | 检查图像采集完成 |
| CM | 检查完成 | 4 | 检查操作完成 |
| RP | 报告 | 5 | 检查报告出具 |
| RD | 阅读 | 6 | 临床医生查阅报告 |

### 5.2 取消状态代码

| 状态代码 | 状态描述 | 说明 |
|---------|---------|------|
| CBK | 取消预约 | 取消已预约的检查 |
| CSC | 取消登记 | 取消检查登记 |
| CIM | 取消图像 | 删除已采集图像 |
| CA | 取消检查 | 取消整个检查流程 |
| CRP | 取消报告 | 撤销已出具报告 |

## 六、关键接口调用示例

### 6.1 检查申请单发送

**服务调用：**
```
方法：HIPMessageServer
Action：AddRisAppBill
```

**请求消息结构：**
```xml
<Request>
    <Header>
        <SourceSystem>RIS</SourceSystem>
        <MessageID>唯一消息ID</MessageID>
    </Header>
    <Body>
        <PATPatientInfo>
            <!-- 患者基本信息 -->
            <PATPatientID>患者主索引</PATPatientID>
            <PATName>患者姓名</PATName>
            <PATDob>出生日期</PATDob>
            <!-- 更多患者信息... -->
        </PATPatientInfo>
        <PATAdmInfo>
            <!-- 就诊信息 -->
            <PAADMVisitNumber>就诊号码</PAADMVisitNumber>
            <PAADMDeptCode>科室代码</PAADMDeptCode>
            <!-- 更多就诊信息... -->
        </PATAdmInfo>
        <AddRisAppBillRt>
            <OrderList>
                <!-- 检查医嘱信息 -->
                <OEORIOrderItemID>医嘱明细ID</OEORIOrderItemID>
                <RISRCode>检查项目代码</RISRCode>
                <RISRDesc>检查项目名称</RISRDesc>
                <!-- 更多医嘱信息... -->
            </OrderList>
        </AddRisAppBillRt>
    </Body>
</Request>
```

### 6.2 检查状态变更回传

**服务调用：**
```
方法：HIPMessageServer
Action：状态变更回传服务编码
```

**请求消息结构：**
```xml
<Request>
    <Header>
        <SourceSystem>PACS</SourceSystem>
        <MessageID>唯一消息ID</MessageID>
    </Header>
    <Body>
        <StatusParameter>
            <PATPatientID>患者登记号</PATPatientID>
            <PAADMVisitNumber>就诊号</PAADMVisitNumber>
            <OEORIOrderItemID>医嘱明细ID</OEORIOrderItemID>
            <RISRExamID>检查号</RISRExamID>
            <RISRSystemType>PACS</RISRSystemType>
            <StatusCode>状态代码</StatusCode>
            <UpdateUserCode>更新人工号</UpdateUserCode>
            <UpdateUserName>更新人姓名</UpdateUserName>
            <UpdateDateTime>更新时间</UpdateDateTime>
        </StatusParameter>
    </Body>
</Request>
```

### 6.3 文档注册

**服务调用：**
```
方法：HIPMessageServer
Action：RegisterDocument
```

**关键字段说明：**
- `DocumentType`: 02001(放射), 02002(超声), 02003(内镜), 02004(心电), 02005(核医学)
- `DocumentContent`: 报告内容Base64编码
- `DocumentPath`: 报告访问URL地址
- `DocumentPicPath`: 图像访问URL地址

## 七、网络配置要求

### 7.1 网络连通性
```
源地址：PACS系统服务器
目标地址：**************
目标端口：1443
协议：HTTPS/TCP
```

### 7.2 防火墙配置
```
入站规则：允许PACS系统IP访问HIS服务器1443端口
出站规则：允许HIS系统访问PACS系统WebService端口
```

### 7.3 SSL证书处理
```
如果是自签名证书，需要在代码中配置忽略证书验证：
- Java: 设置TrustAllX509TrustManager
- .NET: ServicePointManager.ServerCertificateValidationCallback
- Python: 使用verify=False参数
```

## 八、开发实施检查清单

### 8.1 环境准备
- [ ] 获取HIS系统服务器IP和端口访问权限
- [ ] 下载并验证WSDL文件完整性
- [ ] 配置开发环境网络连接
- [ ] 准备SSL证书处理方案

### 8.2 接口开发
- [ ] 生成WebService客户端代理类
- [ ] 实现XML消息序列化/反序列化
- [ ] 开发业务接口调用方法
- [ ] 实现错误处理和重试机制

### 8.3 测试验证
- [ ] 单接口功能测试
- [ ] 业务流程端到端测试
- [ ] 异常场景测试
- [ ] 性能压力测试

### 8.4 部署上线
- [ ] 生产环境网络配置
- [ ] 系统监控和日志配置
- [ ] 故障应急处理预案
- [ ] 用户培训和文档交付

## 九、技术支持联系方式

**东华医为技术支持：**
- 项目联系人：张新莹
- 技术负责人：陈江
- 支持邮箱：[需要向项目组确认]
- 技术热线：[需要向项目组确认]

**注意事项：**
1. 所有接口调用需要记录完整的请求和响应日志
2. 关键业务接口需要实现重试机制
3. 危急值相关接口优先级最高，需要实时处理
4. 定期同步基础字典数据，保持数据一致性

---

**文档编制：** PACS项目实施专家  
**最后更新：** $(date)  
**文档状态：** 正式版本
