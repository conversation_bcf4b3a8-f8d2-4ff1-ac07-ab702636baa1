# 东华医院信息平台系统集成解决方案(检查部分)

**东华医为科技有限公司**

**医院信息平台组**

**2021**年**8月1**日

版本历史

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| 版本  | 作者  | 参与者 | 发布日期 | 修改摘要 |
| V1.0 | 张新莹 | 陈江  | 2021-08-01 |     |

Copyright © 2018 DHC MediWay Technology Co., Ltd. All rights reserved

请不要给第三方传阅

**目 录**

1 文档说明 3

2 接口交互方式 4

3 交互服务流程图 4

4 业务数据交互服务列表 5

5 交互内容说明 6

5.1检查申请单 6

5.2医嘱状态变更 13

5.3检查状态变更回传 15

5.4检查危机值接收 18

5.5检查危机值查看状态 20

5.6文档注册 22

6 基础字典 25

6.1发送医护人员字典信息 25

6.2发送科室字典信息 27

6.3发送病区字典信息 30

6.4发送床位字典信息 32

6.5发送医嘱大类字典信息 34

6.6发送医嘱子类字典信息 35

6.7发送医嘱项字典信息 37

6.8发送医嘱结果状态字典信息 39

7 附录 41

附录一 检查状态字典（绿色部分为本次需要对接的检查状态） 41

附录二 检查文档注册报告内容 44

# 文档说明

本文档描述了东华医为科技有限公司软件系统集成解决方案中与第三方PACS系统进行数据交互的方案，包括了数据交互方式、交互数据内容以及交互流程。

# 接口交互方式

东华医为科技有限公司与第三方PACS系统采用WebService+XML的接口方式进行数据交互。

**交互接口说明**：

<div class="joplin-table-wrapper"><table><tbody><tr><td><p>功能说明</p></td><td><p>根据交互操作服务编码和具体的消息流进行相应的交互操作</p></td></tr><tr><td><p>通讯方式</p></td><td><p>WebService+Xml</p></td></tr><tr><td><p>服务地址</p></td><td><p></p></td></tr><tr><td><p>方法名</p></td><td><p>HIPMessageServer(入参1 action,入参2 message)</p></td></tr><tr><td><p></p></td><td><ol><li>action类型为字符串，服务编号传给该参数</li><li>message类型为标准消息请求流传给该参数</li></ol></td></tr></tbody></table></div>

**申明：所有服务中“示例”仅用于说明**Xml**的总体结构，具体到每个字段的属性名以“请求消息”、“应答消息”定义中为准。**

# 交互服务流程图

请用Visio阅览。

# 业务数据交互服务列表

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| 服务编码（**以项目具体发布为准**） | 服务描述 | 服务提供者 | 服务调用者 | 备注  |
|     | 检查申请单信息发送 | 第三方系统 | 东华HIS系统 |     |
|     | 医嘱状态变更 | 第三方系统 | 东华HIS系统 |     |
|     | 检查登记 | 东华HIS系统 | 第三方系统 |     |
|     | 取消检查登记 | 东华HIS系统 | 第三方系统 |     |
|     | 已有图像 | 东华HIS系统 | 第三方系统 |     |
|     | 取消图像 | 东华HIS系统 | 第三方系统 |     |
|     | 检查完成 | 东华HIS系统 | 第三方系统 |     |
|     | 取消检查 | 东华HIS系统 | 第三方系统 |     |
|     | 已有报告 | 东华HIS系统 | 第三方系统 |     |
|     | 取消报告 | 东华HIS系统 | 第三方系统 |     |
|     | 检查危急值接收 | 东华HIS系统 | 第三方系统 |     |
|     | 检查危急值状态查看 | 第三方系统 | 东华HIS系统 |     |
|     | 文档注册 | 东华HIS系统 | 第三方系统 |     |
|     | 医护人员字典 | 第三方系统 | 东华HIS系统 |     |
|     | 科室字典 | 第三方系统 | 东华HIS系统 |     |
|     | 病区字典 | 第三方系统 | 东华HIS系统 |     |
|     | 床位字典 | 第三方系统 | 东华HIS系统 |     |
|     | 医嘱大类字典 | 第三方系统 | 东华HIS系统 |     |
|     | 医嘱子类字典 | 第三方系统 | 东华HIS系统 |     |
|     | 医嘱项字典 | 第三方系统 | 东华HIS系统 |     |
|     | 医嘱结果状态字典 | 第三方系统 | 东华HIS系统 |     |

# 交互内容说明

## 5.1检查申请单

|     |     |
| --- | --- |
| **接收检查申请单信息** |     |
| 接口方式 | XML+WebService |
| 服务编码 |     |
| 服务名称 | 检查申请单信息发送AddRisAppBill |
| 服务提供者 | PACS系统 |
| 服务调用者 | 平台  |
| 调用时机 | 住院保存/提交检查申请单时发送、门诊计费后发送 |

请求消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| PATPatientInfo | 患者信息集合 |     |     |     |
| HospitalCode | 院区代码 | VARCHAR(30) |     |     |
| HospitalDesc | 院区名称 | VARCHAR(50) |     |     |
| PATPatientID | 患者主索引 | VARCHAR(30) | NOT NULL |     |
| PATDocumentNo | 住院号/病案号 | VARCHAR(20) |     |     |
| PATHealthCardID | 健康卡号 | VARCHAR(18) |     |     |
| PATName | 患者姓名 | VARCHAR(50) | NOT NULL |     |
| PATDob | 患者出生日期 | DATETIME | NOT NULL | YYYY-MM-DD<br><br>hh:mm:ss |
| PATAge | 年龄  | VARCHAR(20) | NOT NULL |     |
| PATSexCode | 患者性别代码 | VARCHAR(1) | NOT NULL |     |
| PATSexDesc | 患者性别描述 | VARCHAR(1) | NOT NULL |     |
| PATMaritalStatusCode | 患者婚姻状况代码 | VARCHAR(2) |     |     |
| PATMaritalStatusDesc | 患者婚姻状况描述 | VARCHAR(20) |     |     |
| PATNationCode | 患者民族代码 | VARCHAR(2) | NOT NULL |     |
| PATNationDesc | 患者民族描述 | VARCHAR(20) | NOT NULL |     |
| PATCountryCode | 患者国籍代码 | VARCHAR(3) | NOT NULL |     |
| PATCountryDesc | 患者国籍描述 | VARCHAR(100) | NOT NULL |     |
| PATMotherID | 患者母亲ID | VARCHAR(30) |     |     |
| PATDeceasedDate | 死亡日期 |     |     |     |
| PATDeceasedTime | 死亡时间 |     |     |     |
| PATTelephone | 患者联系电话 | VARCHAR(20) |     |     |
| PATOccupationCode | 患者职业代码 | VARCHAR(20) |     |     |
| PATOccupationDesc | 患者职业描述 | VARCHAR(50) |     |     |
| PATWorkPlaceName | 患者工作单位名称 | VARCHAR(70) |     |     |
| PATWorkPlaceTelNum | 患者工作单位电话号码 | VARCHAR(20) |     |     |
| PATIdentityNum | 患者证件号码 | VARCHAR(18) |     |     |
| PATIdTypeCode | 患者证件类别代码 | VARCHAR(2) | NOT NULL |     |
| PATIdTypeDesc | 患者证件类别描述 | VARCHAR(20) | NOT NULL |     |
| PATRelationName | 患者联系人姓名 | VARCHAR(50) |     |     |
| PATRelationPhone | 患者联系人电话 | VARCHAR(20) |     |     |
| PATRemarks | 备注  | VARCHAR(200) |     |     |
| PATAdmInfo | 患者就诊信息集合 |     |     |     |
| PAADMVisitNumber | 就诊号码 | VARCHAR(30) | NOT NULL |     |
| PAADMVisitTimes | 住院次数 | INTEGER |     |     |
| PAADMDeptCode | 就诊科室代码 | VARCHAR(10) |     |     |
| PAADMDeptDesc | 就诊科室名称 | VARCHAR(20) |     |     |
| PAADMAdmWardCode | 就诊病区代码 | VARCHAR(50) |     |     |
| PAADMAdmWardDesc | 就诊病区描述 | VARCHAR(50) |     |     |
| PAADMCurBedNo | 病床号 | VARCHAR(50) |     |     |
| PAADMDocCode | 就诊医生代码 | VARCHAR(50) |     |     |
| PAADMDocDesc | 就诊医生名称 | VARCHAR(50) |     |     |
| PAADMStartDate | 就诊日期 |     |     |     |
| PAADMStartTime | 就诊时间 |     |     |     |
| PAADMCurDeptCode | 当前科室代码 | VARCHAR(50) |     |     |
| PAADMCurDeptDesc | 当前科室描述 | VARCHAR(50) |     |     |
| PAAdmStatusCode | 就诊状态代码 | VARCHAR(20) |     |     |
| PAAdmStatusDesc | 就诊状态描述 | VARCHAR(20) |     |     |
| PAADMTypeCode | 就诊类型代码 | VARCHAR(20) |     |     |
| PAADMTypeDesc | 就诊类型描述 | VARCHAR(20) |     |     |
| PAADMFeeTypeCode | 患者费用类型代码 | VARCHAR(20) |     |     |
| PAADMFeeTypeDesc | 患者费用类型描述 | VARCHAR(20) |     |     |
| PAADMHosCode | 医院编码 | VARCHAR(30) |     |     |
| PAADMHosDesc | 医院名称 | VARCHAR(50) |     |     |
| Charger | 收费员 | VARCHAR(50) |     |     |
| ChargeDate | 收费时间 |     |     |     |
| AddRisAppBillRt | 检查申请单信息集合 |     |     |     |
| OrderList | 医嘱信息集合 | OrderList | 医嘱信息集合 | OrderList |
| RISRExamID | 检查号 | VARCHAR(50) |     |     |
| RISRSystemType | 检查系统类型 | VARCHAR(50) |     | 附录一：系统代码 |
| RISRAppNum | 检查申请单号 | VARCHAR(50) |     |     |
| RISRMattersAttention | 检查注意事项 | VARCHAR(1000) |     |     |
| RISRSpecalMedicalRecord | 检查特殊病史编码（高血压/心脏病） | VARCHAR(300) |     |     |
| AppDeptCode | 检查申请科室代码 | VARCHAR(50) |     |     |
| AppDeptDesc | 检查申请科室 | VARCHAR(50) |     |     |
| RISRSubmitDocCode | 检查申请医生代码 | VARCHAR(50) | NOT NULL |     |
| RISRSubmitDocDesc | 检查申请医生描述 | VARCHAR(50) | NOT NULL |     |
| RISRSubmitTime | 检查申请时间 | DATETIME | NOT NULL | YYYY-MM-DD hh:mm:ss |
| RISRAcceptDeptCode | 接收科室代码 | VARCHAR(50) | NOT NULL |     |
| RISRAcceptDeptDesc | 接收科室 | VARCHAR(100) |     |     |
| RISRDeptLocation | 检查科室位置 | VARCHAR(100) |     |     |
| RISRISEmergency | 检查是否加急 | BOOLEAN |     | 1/0 |
| RISRClinicalSymptoms | 检查临床所见 | VARCHAR(200) |     |     |
| OEORIOrderItemID | 医嘱明细ID | VARCHAR(30) | NOT NULL | 这是接口交互唯一主键 |
| RISRPositionCode | 医嘱部位代码 | VARCHAR(30) |     | 多部位之间用上尖号^拼接 |
| RISRPostureCode | 医嘱体位代码 | VARCHAR(30) |     | 多体位之间用上尖号^拼接 |
| RISRCode | 医嘱项目代码 | VARCHAR(30) | NOT NULL |     |
| RISRDesc | 医嘱项目名称 | VARCHAR(50) | NOT NULL |     |
| RISRPrice | 医嘱价格 | VARCHAR(50) | NOT NULL |     |
| RISRNUM | 数量  | VARCHAR(50) | NOT NULL |     |
| OrdSubCatCode | 医嘱子类代码 | VARCHAR(50) | NOT NULL |     |
| OrdSubCatDesc | 医嘱子类描述 | VARCHAR(100) | NOT NULL |     |
| OrdCatCode | 医嘱大类代码 | VARCHAR(50) | NOT NULL |     |
| OrdCatDesc | 医嘱大类描述 | VARCHAR(100) | NOT NULL |     |
| RISRArExaReqSym | 主诉  | VARCHAR(3000) |     |     |
| RISRPatDiag | 诊断  | VARCHAR(300) |     |     |
| RISRArPurpose | 检查目的 | VARCHAR(3000) |     |     |
| OrdBillStatus | 收费状态 | VARCHAR(50) |     |     |
| PhysiCycleCode | 生理周期代码 | VARCHAR(50) |     |     |
| PhysiCycle | 生理周期 | VARCHAR(50) |     |     |
| UpdateUserCode | 最后更新人代码 | VARCHAR(50) |     |     |
| UpdateUserDesc | 最后更新人 | VARCHAR(50) |     |     |
| UpdateDate | 更新日期 |     |     |     |
| UpdateTime | 更新时间 |     |     |     |

|     |
| --- |
| **示例** |
| &lt;Request&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;RIS&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;6&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;PATPatientInfo&gt;<br><br>&lt;HospitalCode&gt;ZSDXFSKQYY&lt;/HospitalCode&gt;<br><br>&lt;HospitalDesc&gt;中山大学附属口腔医院&lt;/HospitalDesc&gt;<br><br>&lt;PATPatientID&gt;**********&lt;/PATPatientID&gt;<br><br>&lt;PATName&gt;陈廷活测试&lt;/PATName&gt;<br><br>&lt;PATDob&gt;1996-03-29&lt;/PATDob&gt;<br><br>&lt;PATAge&gt;26岁&lt;/PATAge&gt;<br><br>&lt;PATSexCode&gt;1&lt;/PATSexCode&gt;<br><br>&lt;PATSexDesc&gt;男&lt;/PATSexDesc&gt;<br><br>&lt;PATMaritalStatusCode&gt;10&lt;/PATMaritalStatusCode&gt;<br><br>&lt;PATMaritalStatusDesc&gt;未婚&lt;/PATMaritalStatusDesc&gt;<br><br>&lt;PATDocumentNo&gt;A00044&lt;/PATDocumentNo&gt;<br><br>&lt;PATNationCode&gt;01&lt;/PATNationCode&gt;<br><br>&lt;PATNationDesc&gt;汉族&lt;/PATNationDesc&gt;<br><br>&lt;PATCountryCode&gt;156&lt;/PATCountryCode&gt;<br><br>&lt;PATCountryDesc&gt;中国&lt;/PATCountryDesc&gt;<br><br>&lt;PATDeceasedDate&gt;&lt;/PATDeceasedDate&gt;<br><br>&lt;PATDeceasedTime&gt;&lt;/PATDeceasedTime&gt;<br><br>&lt;PATHealthCardID&gt;&lt;/PATHealthCardID&gt;<br><br>&lt;PATMotherID&gt;&lt;/PATMotherID&gt;<br><br>&lt;PATOccupationCode&gt;24&lt;/PATOccupationCode&gt;<br><br>&lt;PATOccupationDesc&gt;工人&lt;/PATOccupationDesc&gt;<br><br>&lt;PATWorkPlaceName&gt;-&lt;/PATWorkPlaceName&gt;<br><br>&lt;PATWorkPlaceTelNum&gt;&lt;/PATWorkPlaceTelNum&gt;<br><br>&lt;PATIdentityNum&gt;******************&lt;/PATIdentityNum&gt;<br><br>&lt;PATIdTypeCode&gt;01&lt;/PATIdTypeCode&gt;<br><br>&lt;PATIdTypeDesc&gt;居民身份证&lt;/PATIdTypeDesc&gt;<br><br>&lt;PATRelationName&gt;-&lt;/PATRelationName&gt;<br><br>&lt;PATRelationPhone&gt;16682192600&lt;/PATRelationPhone&gt;<br><br>&lt;PATTelephone&gt;16682192600&lt;/PATTelephone&gt;<br><br>&lt;PATRemarks&gt;**********&lt;/PATRemarks&gt;<br><br>&lt;/PATPatientInfo&gt;<br><br>&lt;PATAdmInfo&gt;<br><br>&lt;PAADMVisitNumber&gt;264&lt;/PAADMVisitNumber&gt;<br><br>&lt;PAADMVisitTimes&gt;1&lt;/PAADMVisitTimes&gt;<br><br>&lt;PAADMTypeCode&gt;I&lt;/PAADMTypeCode&gt;<br><br>&lt;PAADMTypeDesc&gt;住院&lt;/PAADMTypeDesc&gt;<br><br>&lt;PAAdmStatusCode&gt;A&lt;/PAAdmStatusCode&gt;<br><br>&lt;PAAdmStatusDesc&gt;在就诊&lt;/PAAdmStatusDesc&gt;<br><br>&lt;PAADMDocCode&gt;170059&lt;/PAADMDocCode&gt;<br><br>&lt;PAADMDocDesc&gt;李翔&lt;/PAADMDocDesc&gt;<br><br>&lt;PAADMStartDate&gt;2022-07-04&lt;/PAADMStartDate&gt;<br><br>&lt;PAADMStartTime&gt;09:02:06&lt;/PAADMStartTime&gt;<br><br>&lt;PAADMDeptCode&gt;16&lt;/PAADMDeptCode&gt;<br><br>&lt;PAADMDeptDesc&gt;口腔颌面外科&lt;/PAADMDeptDesc&gt;<br><br>&lt;PAADMAdmWardCode&gt;1601&lt;/PAADMAdmWardCode&gt;<br><br>&lt;PAADMAdmWardDesc&gt;一病区&lt;/PAADMAdmWardDesc&gt;<br><br>&lt;PAADMCurBedNo&gt;601&lt;/PAADMCurBedNo&gt;<br><br>&lt;PAADMCurDeptCode&gt;16&lt;/PAADMCurDeptCode&gt;<br><br>&lt;PAADMCurDeptDesc&gt;口腔颌面外科&lt;/PAADMCurDeptDesc&gt;<br><br>&lt;PAADMFeeTypeCode&gt;09&lt;/PAADMFeeTypeCode&gt;<br><br>&lt;PAADMFeeTypeDesc&gt;职工医保&lt;/PAADMFeeTypeDesc&gt;<br><br>&lt;PAADMHosCode&gt;ZSDXFSKQYY&lt;/PAADMHosCode&gt;<br><br>&lt;PAADMHosDesc&gt;中山大学附属口腔医院&lt;/PAADMHosDesc&gt;<br><br>&lt;Charger&gt;&lt;/Charger&gt;<br><br>&lt;ChargeDate&gt;&lt;/ChargeDate&gt;<br><br>&lt;/PATAdmInfo&gt;<br><br>&lt;AddRisAppBillRt&gt;<br><br>&lt;OrderList&gt;<br><br>&lt;RISRArExaReqSym&gt;&lt;/RISRArExaReqSym&gt;<br><br>&lt;RISRPatDiag&gt;1.颌骨肿物?&lt;/RISRPatDiag&gt;<br><br>&lt;RISRArPurpose&gt;&lt;/RISRArPurpose&gt;<br><br>&lt;RISRAppNum&gt;APPI2022070400002&lt;/RISRAppNum&gt;<br><br>&lt;RISRExamID&gt;APPI2022070400002&lt;/RISRExamID&gt;<br><br>&lt;RISRMattersAttention&gt;&lt;/RISRMattersAttention&gt;<br><br>&lt;RISRSpecalMedicalRecord&gt;暂缺&lt;/RISRSpecalMedicalRecord&gt;<br><br>&lt;RISRSubmitDocCode&gt;170059&lt;/RISRSubmitDocCode&gt;<br><br>&lt;RISRSubmitDocDesc&gt;李翔&lt;/RISRSubmitDocDesc&gt;<br><br>&lt;RISRSubmitTime&gt;2022-07-04 09:23:24&lt;/RISRSubmitTime&gt;<br><br>&lt;RISRAcceptDeptCode&gt;20&lt;/RISRAcceptDeptCode&gt;<br><br>&lt;RISRAcceptDeptDesc&gt;放射科&lt;/RISRAcceptDeptDesc&gt;<br><br>&lt;RISRDeptLocation&gt;&lt;/RISRDeptLocation&gt;<br><br>&lt;RISRISEmergency&gt;0&lt;/RISRISEmergency&gt;<br><br>&lt;RISRClinicalSymptoms&gt;&lt;/RISRClinicalSymptoms&gt;<br><br>&lt;AppDeptDesc&gt;口腔颌面外科&lt;/AppDeptDesc&gt;<br><br>&lt;AppDeptCode&gt;16&lt;/AppDeptCode&gt;<br><br>&lt;OEORIOrderItemID&gt;235\|19&lt;/OEORIOrderItemID&gt;<br><br>&lt;RISRPositionCode&gt;&lt;/RISRPositionCode&gt;<br><br>&lt;RISRPostureCode&gt;&lt;/RISRPostureCode&gt;<br><br>&lt;RISRCode&gt;210102015-1#1&lt;/RISRCode&gt;<br><br>&lt;RISRDesc&gt;DR-胸部正位片&lt;/RISRDesc&gt;<br><br>&lt;RISRPrice&gt;79&lt;/RISRPrice&gt;<br><br>&lt;RISRNUM&gt;1&lt;/RISRNUM&gt;<br><br>&lt;OrdSubCatCode&gt;放射申请单&lt;/OrdSubCatCode&gt;<br><br>&lt;OrdSubCatDesc&gt;放射申请单&lt;/OrdSubCatDesc&gt;<br><br>&lt;OrdCatCode&gt;检查类&lt;/OrdCatCode&gt;<br><br>&lt;OrdCatDesc&gt;检查类&lt;/OrdCatDesc&gt;<br><br>&lt;OrdBillStatus&gt;未收费&lt;/OrdBillStatus&gt;<br><br>&lt;PhysiCycleCode&gt;&lt;/PhysiCycleCode&gt;<br><br>&lt;PhysiCycle&gt;&lt;/PhysiCycle&gt;<br><br>&lt;/OrderList&gt;<br><br>&lt;/AddRisAppBillRt&gt;<br><br>&lt;UpdateUserCode&gt;demo&lt;/UpdateUserCode&gt;<br><br>&lt;UpdateUserDesc&gt;&lt;/UpdateUserDesc&gt;<br><br>&lt;UpdateDate&gt;2022-07-04&lt;/UpdateDate&gt;<br><br>&lt;UpdateTime&gt;09:24:52&lt;/UpdateTime&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Request&gt; |

应答消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| ResultCode | 响应码 | VARCHAR(6) | NOT NULL | 0：成功 -1:失败 |
| ResultContent | 响应信息 | VARCHAR(300) | NOT NULL |     |

|     |
| --- |
| **示例** |
| &lt;Response&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ResultCode&gt;0&lt;/ResultCode&gt;<br><br>&lt;ResultContent&gt;成功&lt;/ResultContent&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Response&gt; |

## 5.2医嘱状态变更

|     |     |
| --- | --- |
| **医嘱状态变更** |     |
| 接口方式 | XML+WebService |
| 服务编码 |     |
| 服务名称 | 医嘱状态变更UpdateOrdersStatus |
| 服务提供者 | 第三方系统 |
| 服务调用者 | 平台  |
| 调用时机 | 医嘱状态发生变化时调用 |

请求消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| BusinessFieldCode | 业务域 | VARCHAR(50) |     |     |
| HospitalCode | 医院代码 | VARCHAR(50) |     |     |
| PATPatientID | 患者主索引 | VARCHAR(30) | NOT NULL |     |
| PAADMVisitNumber | 就诊号码 | VARCHAR(30) | NOT NULL |     |
| PAAdmTypeCode | 就诊类型代码 | VARCHAR(5) | NOT NULL | 用于区分O:门诊、H:体检、E:急诊病人就诊信息 |
| OEORIInfoList | 医嘱信息列表 |     |     |     |
| OEORIInfo | 医嘱信息 |     |     |     |
| OEORIOrderItemID | 医嘱明细ID | VARCHAR(30) | NOT NULL |     |
| OEORIStatusCode | 医嘱状态代码 | VARCHAR(10) | NOT NULL | V核实，D停止，E执行<br><br>U作废，C撤销，I未审核<br><br>P 已处理 |
| OEORIOrdSubCatCode | 医嘱子类代码 | VARCHAR(30) |     |     |
| OEORIOrdSubCatDesc | 医嘱子类描述 | VARCHAR(50) |     |     |
| OEORIOrdCatCode | 医嘱大类代码 | VARCHAR(30) |     |     |
| OEORIOrdCatDesc | 医嘱大类描述 | VARCHAR(50) |     |     |
| OEORIParentOrderID | 父医嘱ID | VARCHAR(50) |     |     |
| OEORIEnterDeptCode | 医嘱开立科室代码 | VARCHAR(100) |     |     |
| OEORIEnterDeptDesc | 医嘱开立科室 | VARCHAR(100) |     |     |
| OEORIExecDeptCode | 医嘱执行科室代码 | VARCHAR(100) |     |     |
| OEORIExecDeptDesc | 医嘱执行科室 | VARCHAR(100) |     |     |
| UpdateUserCode | 最后更新人编码 | VARCHAR(20) | NOT NULL |     |
| UpdateUserDesc | 最后更新人描述 | VARCHAR(50) | NOT NULL |     |
| UpdateDate | 最后更新日期 | DATE | NOT NULL | YYYY-MM-DD |
| UpdateTime | 最后更新时间 | TIME | NOT NULL | hh:mm:ss |

|     |
| --- |
| **示例** |
| &lt;Request&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;02&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;22&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;UpdateOrdersRt&gt;<br><br>&lt;BusinessFieldCode&gt;00002&lt;/BusinessFieldCode&gt;<br><br>&lt;HospitalCode&gt;PAXRMYY&lt;/HospitalCode&gt;<br><br>&lt;PATPatientID&gt;**********&lt;/PATPatientID&gt;<br><br>&lt;PAADMVisitNumber&gt;15&lt;/PAADMVisitNumber&gt;<br><br>&lt;PAADMEncounterTypeCode&gt;I&lt;/PAADMEncounterTypeCode&gt;<br><br>&lt;OEORIInfoList&gt;<br><br>&lt;OEORIInfo&gt;<br><br>&lt;OEORIOrderItemID&gt;69\|22&lt;/OEORIOrderItemID&gt;<br><br>&lt;OEORIStatusCode&gt;D&lt;/OEORIStatusCode&gt;<br><br>&lt;OEORIOrdSubCatCode&gt;西药注射剂&lt;/OEORIOrdSubCatCode&gt;<br><br>&lt;OEORIOrdSubCatDesc&gt;西药注射剂&lt;/OEORIOrdSubCatDesc&gt;<br><br>&lt;OEORIOrdCatCode&gt;西药&lt;/OEORIOrdCatCode&gt;<br><br>&lt;OEORIOrdCatDesc&gt;西药&lt;/OEORIOrdCatDesc&gt;<br><br>&lt;OEORIParentOrderID&gt;&lt;/OEORIParentOrderID&gt;<br><br>&lt;OEORIEnterDeptCode&gt;PA0040&lt;/OEORIEnterDeptCode&gt;<br><br>&lt;OEORIEnterDeptDesc&gt;感染科&lt;/OEORIEnterDeptDesc&gt;<br><br>&lt;OEORIExecDeptCode&gt;PA0061&lt;/OEORIExecDeptCode&gt;<br><br>&lt;OEORIExecDeptDesc&gt;西药房&lt;/OEORIExecDeptDesc&gt;<br><br>&lt;/OEORIInfo&gt;<br><br>&lt;/OEORIInfoList&gt;<br><br>&lt;UpdateUserCode&gt;demo&lt;/UpdateUserCode&gt;<br><br>&lt;UpdateUserDesc&gt;demo&lt;/UpdateUserDesc&gt;<br><br>&lt;UpdateDate&gt;2021-05-13&lt;/UpdateDate&gt;<br><br>&lt;UpdateTime&gt;10:15:32&lt;/UpdateTime&gt;<br><br>&lt;/UpdateOrdersRt&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Request&gt; |

应答消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| ResultCode | 响应码 | VARCHAR(6) | NOT NULL | 0：成功 -1:失败 |
| ResultContent | 响应信息 | VARCHAR(300) | NOT NULL |     |

|     |
| --- |
| **示例** |
| &lt;Response&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ResultCode&gt;0&lt;/ResultCode&gt;<br><br>&lt;ResultContent&gt;成功&lt;/ResultContent&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Response&gt; |

## 5.3检查状态变更回传

|     |     |
| --- | --- |
| **状态变更信息** |     |
| 接口方式 | XML+WebService |
| 服务编码 | 附录一：服务编码 |
| 服务名称 | 状态变更回传HIS |
| 服务提供者 | 平台  |
| 服务调用者 | 第三方系统 |
| 调用时机 | 当检查状态节点发生变化时调用 |

**请求消息**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **约束** | **说明** |
| SourceSystem | 系统代码 | varchar(20) | 必填  | 总线接入系统代码 |
| MessageID | 消息id | varchar(100) |     |     |
| StatusParameter （1..N） |     |     |     |     |
| PATPatientID | 患者登记号 | varchar(100) |     |     |
| PAADMVisitNumber | 患者就诊号 | varchar(100) | 必填  |     |
| OEORIOrderItemID | 医嘱id | varchar(100) | 条件必填 | 检验检查闭环必填 |
| OEORIOrdExecID | 医嘱执行记录id | varchar(100) | 条件必填 | 口服药、静配闭环必填 |
| SpecimenID | 检验条码号/标本号 | varchar(100) | 条件必填 | 检验闭环，生成条码号后必填 |
| RISRExamID | 检查号 | varchar(100) | 条件必填 | 检查闭环，登记产生检查号后必填 |
| RISRSystemType | 系统类型 | varchar(50) | 条件必填 | 附录一：系统代码 |
| Position | 检查部位 | varchar(200) |     | 多部位时将多个部位以@@分隔 |
| OperAppID | 手术申请id | varchar(100) | 条件必填 | 手术闭环必填 |
| BloodAppID | 输血申请id | varchar(100) | 条件必填 | 输血闭环必填 |
| BloodBagNo | 用血血袋号 | varchar(100) | 条件必填 | 输血分配血袋后必填 |
| ConsultAppID | 会诊申请id | varchar(100) | 条件必填 | 会诊闭环必填 |
| StatusCode | 状态代码 | varchar(10) | 必填  | 附录一：状态代码 |
| UpdateUserCode | 更新人工号 | varchar(50) | 必填  |     |
| UpdateUserName | 更新人姓名 | varchar(50) | 必填  |     |
| UpdateDateTime | 更新日期时间 | varchar(20) | 必填  | yyyy-mm-dd hh24:mi:ss |
| ESNotes | 备注  | varchar(100) |     | 预约备注：预约日期^开始时间-结束时间^排队号^资源代码^资源描述^资源地址^改约标识；<br><br>&nbsp;取消预约备注:预约日期^开始时间-结束时间^资源代码^资源描述； |

|     |
| --- |
| **示例** |
| &lt;Request&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;PACS&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;StatusParameter&gt;<br><br>&lt;PATPatientID&gt;**********&lt;/PATPatientID&gt;<br><br>&lt;PAADMVisitNumber&gt;108&lt;/PAADMVisitNumber&gt;<br><br>&lt;OEORIOrderItemID&gt;101\|10&lt;/OEORIOrderItemID&gt;<br><br>&lt;OEORIOrdExecID&gt;&lt;/OEORIOrdExecID&gt;<br><br>&lt;SpecimenID&gt;&lt;/SpecimenID&gt;<br><br>&lt;RISRExamID&gt;&lt;/RISRExamID&gt;<br><br>&lt;RISRSystemType&gt;PACS&lt;/RISRSystemType&gt;<br><br>&lt;Position&gt;&lt;/Position&gt;<br><br>&lt;OperAppID&gt;&lt;/OperAppID&gt;<br><br>&lt;BloodAppID&gt;&lt;/BloodAppID&gt;<br><br>&lt;BloodBagNo&gt;&lt;/BloodBagNo&gt;<br><br>&lt;ConsultAppID&gt;&lt;/ConsultAppID&gt;<br><br>&lt;StatusCode&gt;SC&lt;/StatusCode&gt;<br><br>&lt;UpdateUserCode&gt;ys01&lt;/UpdateUserCode&gt;<br><br>&lt;UpdateUserName&gt;医生01&lt;/UpdateUserName&gt;<br><br>&lt;UpdateDateTim/&gt;2022-06-21 10:06:00&lt;/UpdateDateTime&gt;<br><br>&lt;ESNotes&gt;&lt;/ESNotes&gt;<br><br>&lt;/StatusParameter&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Request&gt; |
|     |
|     |

**应答消息**：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| ResultCode | 响应码 | VARCHAR(6) | NOT NULL | 0：成功 -1:失败 |
| ResultContent | 响应信息 | VARCHAR(300) | NOT NULL |     |

|     |
| --- |
| **示例** |
| &lt;Response&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ResultCode&gt;0&lt;/ResultCode&gt;<br><br>&lt;ResultContent&gt;成功&lt;/ResultContent&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Response&gt; |

## 5.4检查危急值接收

|     |     |
| --- | --- |
| **检查危急值接收** |     |
| 接口方式 | XML+WebService |
| 服务编码 |     |
| 服务名称 | 检查危机值接收ReCriticalValues |
| 服务提供者 | 平台  |
| 服务调用者 | 检查系统 |
| 调用时机 | 出现危急值时调用 |

请求消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| PATPatientID | 患者主索引 | VARCHAR(30) | NOT NULL |     |
| PAADMVisitNumber | 就诊号码 | VARCHAR(30) | NOT NULL |     |
| RISRExamID | 检查号 | VARCHAR(50) | NOT NULL |     |
| OEORIOrderItemIDList 医嘱明细ID集合 |     |     |     |     |
| OEORIOrderItemID | 医嘱明细ID | VARCHAR(30) | NOT NULL |     |
| ReportType | 报告类型 | VARCHAR(30) | NOT NULL | 2病理,3心电,4超声,5内镜,6放射 |
| RISRReportDocCode | 报告医生代码 | VARCHAR(50) | NOT NULL |     |
| RISRFinalCheckDate | 终审日期 | DATE | NOT NULL | YYYY-MM-DD |
| RISRFinalCheckTime | 终审时间 | TIME | NOT NULL | hh:mm:ss |
| RISPCriticalValueDetail | 危急值报告内容 | VARCHAR(600) | NOT NULL |     |
| UpdateUserCode | 最后更新人编码 | VARCHAR(20) | NOT NULL |     |
| UpdateDate | 最后更新日期 | DATE | NOT NULL | YYYY-MM-DD |
| UpdateTime | 最后更新时间 | TIME | NOT NULL | hh:mm:ss |

|     |
| --- |
| **示例** |
| &lt;Request&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ReCriticalValuesRt&gt;<br><br>&lt;PATPatientID&gt;**********&lt;/PATPatientID&gt;<br><br>&lt;PAADMVisitNumber&gt;108&lt;/PAADMVisitNumber&gt;<br><br>&lt;RISRExamID&gt;11&lt;/RISRExamID&gt;<br><br>&lt;OEORIOrderItemIDList&gt;<br><br>&lt;OEORIOrderItemID&gt;101\|20&lt;/OEORIOrderItemID&gt;<br><br>&lt;ReportType&gt;6&lt;/ReportType&gt;<br><br>&lt;/OEORIOrderItemIDList&gt;<br><br>&lt;RISRReportDocCode&gt;ys01&lt;/RISRReportDocCode&gt;<br><br>&lt;RISRFinalCheckDate&gt;2022-06-18&lt;/RISRFinalCheckDate&gt;<br><br>&lt;RISRFinalCheckTime&gt;11:40:00&lt;/RISRFinalCheckTime&gt;<br><br>&lt;RISPCriticalValueDetail&gt;非常紧急&lt;/RISPCriticalValueDetail&gt;<br><br>&lt;UpdateUserCode&gt;ys01&lt;/UpdateUserCode&gt;<br><br>&lt;UpdateDate&gt;2022-06-18&lt;/UpdateDate&gt;<br><br>&lt;UpdateTime&gt;11:40:00&lt;/UpdateTime&gt;<br><br>&lt;/ReCriticalValuesRt&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Request&gt; |

应答消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| ResultCode | 响应码 | VARCHAR(6) | NOT NULL | 0：成功 -1:失败 |
| ResultContent | 响应信息 | VARCHAR(300) | NOT NULL |     |

|     |
| --- |
| **示例** |
| &lt;Response&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;02&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;39&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ResultCode&gt;0&lt;/ResultCode&gt;<br><br>&lt;ResultContent&gt;成功&lt;/ResultContent&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Response&gt; |

## 5.5检查危急值查看状态

|     |     |
| --- | --- |
| **检查危急值查看状态** |     |
| 接口方式 | XML+WebService |
| 服务编码 |     |
| 服务名称 | 检查危机值查看状态RisCriticalValuesCheck |
| 服务提供者 | 平台  |
| 服务调用者 | HIS |
| 调用时机 | 进行危急值反馈时调用 |

请求消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| PATPatientID | 患者主索引 | VARCHAR(30) | NOT NULL |     |
| PAADMVisitNumber | 就诊号码 | VARCHAR(30) | NOT NULL |     |
| SpecimenID | 检查号 | VARCHAR(50) | NOT NULL |     |
| OEORIOrdSubCatCode | 医嘱子类代码 | VARCHAR(20) | NOT NULL |     |
| OEORIOrdSubCatDesc | 医嘱子类名称 | VARCHAR(20) | NOT NULL |     |
| OEORIOrdCatCode | 医嘱大类代码 | VARCHAR(20) | NOT NULL |     |
| OEORIOrdCatDesc | 医嘱大类名称 | VARCHAR(20) | NOT NULL |     |
| OEORIOrdItemID | 医嘱明细ID | VARCHAR(30) | NOT NULL |     |
| CheckDate | 查看日期 | DATE | NOT NULL | YYYY-MM-DD |
| CheckTime | 查看时间 | TIME | NOT NULL | hh:mm:ss |
| CheckPerson | 查看人 | VARCHAR(20) | NOT NULL |     |
| Telephone | 电话  | VARCHAR(20) |     |     |
| Remark | 备注  | VARCHAR(200) |     |     |
| UpdateUserCode | 最后更新人编码 | VARCHAR(20) | NOT NULL |     |
| UpdateDate | 最后更新日期 | DATE | NOT NULL | YYYY-MM-DD |
| UpdateTime | 最后更新时间 | TIME | NOT NULL | hh:mm:ss |
| Advice | 处理意见 | VARCHAR(500) |     |     |
| ContactPerson | 联系人姓名 | VARCHAR(30) |     |     |

|     |
| --- |
| **示例** |
| &lt;Request&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;02&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;694&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;AddReCriticalValuesRt&gt;<br><br>&lt;PATPatientID&gt;**********&lt;/PATPatientID&gt;<br><br>&lt;PAADMVisitNumber&gt;165961&lt;/PAADMVisitNumber&gt;<br><br>&lt;SpecimenID&gt;412184&lt;/SpecimenID&gt;<br><br>&lt;OEORIOrdSubCatCode&gt;1603&lt;/OEORIOrdSubCatCode&gt;<br><br>&lt;OEORIOrdSubCatDesc&gt;CT&lt;/OEORIOrdSubCatDesc&gt;<br><br>&lt;OEORIOrdCatCode&gt;16&lt;/OEORIOrdCatCode&gt;<br><br>&lt;OEORIOrdCatDesc&gt;检查&lt;/OEORIOrdCatDesc&gt;<br><br>&lt;OEORIOrdItemID&gt;165537\|36&lt;/OEORIOrdItemID&gt;<br><br>&lt;CheckDate&gt;2021-04-26&lt;/CheckDate&gt;<br><br>&lt;CheckTime&gt;18:22:09&lt;/CheckTime&gt;<br><br>&lt;CheckPerson&gt;郭春垣&lt;/CheckPerson&gt;<br><br>&lt;Telephone&gt;18770035502&lt;/Telephone&gt;<br><br>&lt;Remark&gt;已通知&lt;/Remark&gt;<br><br>&lt;UpdateUserCode&gt;demo&lt;/UpdateUserCode&gt;<br><br>&lt;UpdateDate&gt;2021-04-26&lt;/UpdateDate&gt;<br><br>&lt;UpdateTime&gt;18:22:09&lt;/UpdateTime&gt;<br><br>&lt;Advice&gt;-&lt;/Advice&gt;<br><br>&lt;ContactPerson&gt;-&lt;/ContactPerson&gt;<br><br>&lt;/AddReCriticalValuesRt&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Request&gt; |

应答消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| ResultCode | 响应码 | VARCHAR(6) | NOT NULL | 0：成功 -1:失败 |
| ResultContent | 响应信息 | VARCHAR(300) | NOT NULL |     |

|     |
| --- |
| **示例** |
| &lt;Response&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ResultCode&gt;0&lt;/ResultCode&gt;<br><br>&lt;ResultContent&gt;成功&lt;/ResultContent&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Response&gt; |

## 5.6文档注册

|     |     |
| --- | --- |
| **文档注册** |     |
| 接口方式 | XML+WebService |
| 服务编码 |     |
| 服务名称 | 文档注册RegisterDocument |
| 服务提供者 | 平台  |
| 服务调用者 | 第三方系统 |
| 调用时机 | 第三方报告文档生成后调用 |

请求消息：

**代码**

**名称**

**数据类型**

**是否必输**

**备注**

SourceSystem

消息来源

VARCHAR(50)

NOT NULL

MessageID

消息ID

VARCHAR(30)

NOT NULL

OrganizationCode

医疗机构编码

VARCHAR(30)

NOT NULL

默认传0001

PATPatientID

患者主索引

VARCHAR(30)

NOT NULL

患者在医疗机构的唯一标识

PATPatientName

姓名

VARCHAR(30)

NOT NULL

PAADMVisitNumber

就诊号码

VARCHAR(30)

NOT NULL

患者每次就诊的唯一标识

RISRExamID

检查号

VARCHAR(50)

SpecimenID

样本（条码号）

VARCHAR(20)

OEORIOrderItemID

医号嘱明细ID

VARCHAR(20)

DocumentType

文档类别

VARCHAR(3)

NOT NULL

|     |     |
| --- | --- |
| 02001 | 放射  |
| 02002 | 超声  |
| 02003 | 内镜  |
| 02004 | 心电  |
| 02005 | 核医学 |

DocumentID

文档ID

VARCHAR(30)

NOT NULL

源系统内该文档唯一标识

DocumentContent

文档内容

NOT NULL

具体文档内容见临床文档,内容转换成BASE64格式

按报告类型见附录二、附录三

DocumentPath

文档路径

VARCHAR(100)

NOT NULL

Url

DocumentPicPath

图像路径

VARCHAR(100)

NOT NULL

UpdateUserCode

最后更新人编码

VARCHAR(20)

NOT NULL

UpdateDate

最后更新日期

DATE

NOT NULL

YYYY-MM-DD

UpdateTime

最后更新时间

TIME

NOT NULL

hh:mm:ss

|     |
| --- |
| **示例** |
| &lt;Request&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;RegisterDocumentRt&gt;<br><br>&lt;OrganizationCode&gt;2&lt;/OrganizationCode&gt;<br><br>&lt;PATPatientID&gt;**********&lt;/PATPatientID&gt;<br><br>&lt;PATPatientName&gt;周正祥&lt;/PATPatientName&gt;<br><br>&lt;PAADMVisitNumber&gt;108&lt;/PAADMVisitNumber&gt;<br><br>&lt;RISRExamID&gt;11&lt;/RISRExamID&gt;<br><br>&lt;SpecimenID&gt;&lt;/SpecimenID&gt;<br><br>&lt;OEORIOrderItemID&gt;101\|20&lt;/OEORIOrderItemID&gt;<br><br>&lt;DocumentType&gt;02001&lt;/DocumentType&gt;<br><br>&lt;DocumentID&gt;111&lt;/DocumentID&gt;<br><br>&lt;DocumentContent&gt;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&lt;/DocumentContent&gt;<br><br>&lt;DocumentPath&gt;https://***************:2443/hsb/csp/Index.csp&lt;/DocumentPath&gt;<br><br>&lt;DocumentPicPath&gt;https://***************:2443/hsb/csp/Index.csp&lt;/DocumentPicPath&gt;<br><br>&lt;UpdateUserCode&gt;ys01&lt;/UpdateUserCode&gt;<br><br>&lt;UpdateDate&gt;2022-06-18&lt;/UpdateDate&gt;<br><br>&lt;UpdateTime&gt;10:06:00&lt;/UpdateTime&gt;<br><br>&lt;/RegisterDocumentRt&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Request&gt; |

应答消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| ResultCode | 响应码 | VARCHAR(6) | NOT NULL | 0：成功 -1:失败 |
| ResultContent | 响应信息 | VARCHAR(300) | NOT NULL |     |

|     |
| --- |
| **示例** |
| &lt;Response&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;01&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ResultCode&gt;0&lt;/ResultCode&gt;<br><br>&lt;ResultContent&gt;成功&lt;/ResultContent&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Response&gt; |

# 基础字典

## 6.1发送医护人员字典信息

|     |     |
| --- | --- |
| **发送医护人员字典信息** |     |
| 接口方式 | XML+WebService |
| 服务编码 |     |
| 服务名称 | 发送医护人员字典信息SendManagerDataInfo |
| 服务提供者 | 第三方系统 |
| 服务调用者 | 主数据平台 |
| 调用时机 |     |

请求消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| CTCP_BirthDate | 出生日期 | DATE |     | YYYY-MM-DD |
| CTCP_BirthPlace | 出生地 | VARCHAR(200) |     |     |
| CTCP_Code | 代码  | VARCHAR(20) | NOT NULL | 职工代码 |
| CTCP_CodesystemCode | 代码表类型 | VARCHAR(50) | NOT NULL | CT_CareProv |
| CTCP_DeptCode | 所属科室 | VARCHAR(50) |     |     |
| CTCP_Desc | 描述  | VARCHAR(100) | NOT NULL | 职工姓名 |
| CTCP_EndDate | 有效结束日期 | DATE |     | YYYY-MM-DD |
| CTCP_HosCode | 医疗机构代码 | VARCHAR(10) | NOT NULL |     |
| CTCP_IDCardNO | 身份证号 | VARCHAR(30) |     |     |
| CTCP_StaffType | 医护人员类型 | VARCHAR(50) |     | DOCTOR医生<br><br>NURSE护士<br><br>Technician技术人员<br><br>Pharmacist药剂师 |
| CTCP_Name | 姓名  | VARCHAR(100) |     |     |
| CTCP_JobCode | 职务  | VARCHAR(100) |     |     |
| CTCP_Remarks | 备注  | VARCHAR(100) |     |     |
| CTCP_SexCode | 性别  | VARCHAR(1) |     | 1 男 2女 |
| CTCP_StartDate | 有效开始日期 | DATE |     | YYYY-MM-DD |
| CTCP_Status | 状态  | VARCHAR(2) | NOT NULL | 1启用0停用-1删除 |
| CTCP_UpdateUserCode | 最后更新人编码 | VARCHAR(20) | NOT NULL |     |
| CTCP_UpdateDate | 最后更新日期 | DATE |     | YYYY-MM-DD |
| CTCP_UpdateTime | 最后更新时间 | TIME |     | hh:mm:ss |
| CTCP_ExtranetURL |     | VARCHAR(50) |     |     |
| CTCP_IntranetURL |     | VARCHAR(50) |     |     |

|     |
| --- |
| **示例** |
| &lt;Request&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;SYS0004&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;CT_CareProvList&gt;<br><br>&lt;CT_CareProv&gt;<br><br>&lt;BusinessFieldCode&gt;00002&lt;/BusinessFieldCode&gt;<br><br>&lt;CTCP_Code&gt;1115&lt;/CTCP_Code&gt;<br><br>&lt;CTCP_Desc&gt;明杨&lt;/CTCP_Desc&gt;<br><br>&lt;CTCP_Name&gt;明杨&lt;/CTCP_Name&gt;<br><br>&lt;CTCP_SexCode&gt;&lt;/CTCP_SexCode&gt;<br><br>&lt;CTCP_BirthDate&gt;&lt;/CTCP_BirthDate&gt;<br><br>&lt;CTCP_IDCardNO&gt;&lt;/CTCP_IDCardNO&gt;<br><br>&lt;CTCP_JobCode&gt;&lt;/CTCP_JobCode&gt;<br><br>&lt;CTCP_DeptCode&gt;PA0062&lt;/CTCP_DeptCode&gt;<br><br>&lt;CTCP_BirthPlace&gt;&lt;/CTCP_BirthPlace&gt;<br><br>&lt;CTCP_StartDate&gt;2008-04-12&lt;/CTCP_StartDate&gt;<br><br>&lt;CTCP_EndDate&gt;&lt;/CTCP_EndDate&gt;<br><br>&lt;CTCP_CodesystemCode&gt;CT_CareProv&lt;/CTCP_CodesystemCode&gt;<br><br>&lt;CTCP_Status&gt;1&lt;/CTCP_Status&gt;<br><br>&lt;CTCP_UpdateUserCode&gt;无&lt;/CTCP_UpdateUserCode&gt;<br><br>&lt;CTCP_UpdateDate&gt;&lt;/CTCP_UpdateDate&gt;<br><br>&lt;CTCP_UpdateTime&gt;&lt;/CTCP_UpdateTime&gt;<br><br>&lt;CTCP_Remarks&gt;&lt;/CTCP_Remarks&gt;<br><br>&lt;CTCP_HosCode&gt;PAXRMYY&lt;/CTCP_HosCode&gt;<br><br>&lt;CTCP_StaffType&gt;&lt;/CTCP_StaffType&gt;<br><br>&lt;CTCP_ExtranetURL&gt;&lt;/CTCP_ExtranetURL&gt;<br><br>&lt;CTCP_IntranetURL&gt;&lt;/CTCP_IntranetURL&gt;<br><br>&lt;/CT_CareProv&gt;<br><br>&lt;/CT_CareProvList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Request&gt; |

应答消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| ResultCode | 响应码 | VARCHAR(6) | NOT NULL | 0：成功 -1:失败 |
| ResultContent | 响应信息 | VARCHAR(300) | NOT NULL |     |
| SuccessIDList | 成功记录id列表 |     |     |     |
| RowID | rowid | VARCHAR(300) |     |     |

|     |
| --- |
| **示例** |
| &lt;Response&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ResultCode&gt;0&lt;/ResultCode&gt;<br><br>&lt;ResultContent&gt;成功&lt;/ResultContent&gt;<br><br>&lt;SuccessIDList&gt;<br><br>&lt;RowID&gt;1&lt;/RowID&gt;<br><br>&lt;RowID&gt;2&lt;/RowID&gt;<br><br>&lt;/SuccessIDList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Response&gt; |

## 6.2发送科室字典信息

|     |     |
| --- | --- |
| **发送科室字典信息** |     |
| 接口方式 | XML+WebService |
| 服务编码 |     |
| 服务名称 | 发送科室字典信息SendManagerDataInfo |
| 服务提供者 | 第三方系统 |
| 服务调用者 | 主数据平台 |
| 调用时机 |     |

请求消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL | 自增id |
| CTD_CategoryCode | 标识分类代码 | VARCHAR(10) |     |     |
| CTD_Code | 科室代码 | VARCHAR(50) | NOT NULL | 科室编码 |
| CTD_CodesystemCode | 代码表类型 | VARCHAR(50) | NOT NULL | CT_Dept |
| CTD_Desc | 科室描述 | VARCHAR(100) | NOT NULL | 科室名称 |
| CTD_Spell | 拼音  | VARCHAR(100) |     |     |
| CTD_Category | 科室类别 | Varchar(50) | NOT NULL | OR 诊室<br><br>OP 手术室<br><br>W 护理单元<br><br>EM 急诊科室<br><br>E 临床科室<br><br>D 药房<br><br>O 管理科室<br><br>C 收费科室 |
| CTD_Property | 科室性质 | Varchar(50) |     |     |
| CTD_Rank | 科室级次 | Varchar(50) | NOT NULL | 1、2、3、4 |
| CTD_ DepartNature | 部门属性 | Varchar(50) | NOT NULL | 0 大科<br><br>1 科室<br><br>2 医疗组<br><br>3门诊医疗组末级<br><br>4住院医疗组末级<br><br>5 其他医疗组末级 |
| CTD_EndDate | 有效结束日期 | DATE |     | YYYY-MM-DD |
| CTD_HosCode | 医院编号 | VARCHAR(10) | NOT NULL |     |
| CTD_OfficeAddress | 工作地址 | VARCHAR(200) |     |     |
| CTD_OfficePhone | 工作联系电话 | VARCHAR(20) |     |     |
| CTD_ParentDeptCode | 上级科室代码 | VARCHAR(50) |     |     |
| CTD_GroupCode | 科室部门组代码 | Varchar(50) |     |     |
| CTD_GroupDesc | 科室部门组描述 | Varchar(50) |     |     |
| CTD_Remarks | 备注  | VARCHAR(100) |     |     |
| CTD_StartDate | 有效开始日期 | DATE | NOT NULL | YYYY-MM-DD |
| CTD_Status | 状态  | VARCHAR(2) | NOT NULL | 1启用0停用-1删除 |
| CTD_UpdateUserCode | 最后更新人编码 | VARCHAR(20) | NOT NULL |     |
| CTD_UpdateDate | 最后更新日期 | DATE |     | YYYY-MM-DD |
| CTD_UpdateTime | 最后更新时间 | TIME |     | hh:mm:ss |

|     |
| --- |
| **示例** |
| &lt;Request&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;CT_DeptList&gt;<br><br>&lt;CT_Dept&gt;<br><br>&lt;CTD_CategoryCode&gt;标识分类代码&lt;/CTD_CategoryCode&gt;<br><br>&lt;CTD_Code&gt;科室代码&lt;/CTD_Code&gt;<br><br>&lt;CTD_CodesystemCode&gt;代码表类型&lt;/CTD_CodesystemCode&gt;<br><br>&lt;CTD_Desc&gt;科室描述&lt;/CTD_Desc&gt;<br><br>&lt;CTD_Spell&gt;拼音&lt;/CTD_Spell&gt;<br><br>&lt;CTD_Category&gt;科室类别&lt;/CTD_Category&gt;<br><br>&lt;CTD_Property&gt;科室性质&lt;/CTD_Property&gt;<br><br>&lt;CTD_Rank&gt;科室级次&lt;/CTD_Rank&gt;<br><br>&lt;CTD_DepartNature&gt;部门属性&lt;/CTD_DepartNature&gt;<br><br>&lt;CTD_EndDate&gt;有效结束日期&lt;/CTD_EndDate&gt;<br><br>&lt;CTD_HosCode&gt;医院编号&lt;/CTD_HosCode&gt;<br><br>&lt;CTD_OfficeAddress&gt;工作地址&lt;/CTD_OfficeAddress&gt;<br><br>&lt;CTD_OfficePhone&gt;工作联系电话&lt;/CTD_OfficePhone&gt;<br><br>&lt;CTD_ParentDeptCode&gt;上级科室代码&lt;/CTD_ParentDeptCode&gt;<br><br>&lt;CTD_GroupCode&gt;科室部门组代码&lt;/CTD_GroupCode&gt;<br><br>&lt;CTD_GroupDesc&gt;科室部门组描述&lt;/CTD_GroupDesc&gt;<br><br>&lt;CTD_Remarks&gt;备注&lt;/CTD_Remarks&gt;<br><br>&lt;CTD_StartDate&gt;有效开始日期&lt;/CTD_StartDate&gt;<br><br>&lt;CTD_Status&gt;状态（1启用0停用-1删除）&lt;/CTD_Status&gt;<br><br>&lt;CTD_UpdateUserCode&gt;最后更新人编码&lt;/CTD_UpdateUserCode&gt;<br><br>&lt;CTD_UpdateDate&gt;最后更新日期&lt;/CTD_UpdateDate&gt;<br><br>&lt;CTD_UpdateTime&gt;最后更新时间&lt;/CTD_UpdateTime&gt;<br><br>&lt;/CT_Dept&gt;<br><br>&lt;/CT_DeptList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Request&gt; |

应答消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| ResultCode | 响应码 | VARCHAR(6) | NOT NULL | 0：成功 -1:失败 |
| ResultContent | 响应信息 | VARCHAR(300) | NOT NULL |     |
| SuccessIDList | 成功记录id列表 |     |     |     |
| RowID | rowid | VARCHAR(300) |     |     |

|     |
| --- |
| **示例** |
| &lt;Response&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ResultCode&gt;0&lt;/ResultCode&gt;<br><br>&lt;ResultContent&gt;成功&lt;/ResultContent&gt;<br><br>&lt;SuccessIDList&gt;<br><br>&lt;RowID&gt;1&lt;/RowID&gt;<br><br>&lt;RowID&gt;2&lt;/RowID&gt;<br><br>&lt;/SuccessIDList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Response&gt; |

## 6.3发送病区字典信息

|     |     |
| --- | --- |
| **发送病区字典信息** |     |
| 接口方式 | XML+WebService |
| 服务编码 |     |
| 服务名称 | 发送病区字典信息SendManagerDataInfo |
| 服务提供者 | 第三方系统 |
| 服务调用者 | 主数据平台 |
| 调用时机 |     |

请求消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| CTW_Code | 病区代码 | VARCHAR(50) | NOT NULL |     |
| CTW_CodesystemCode | 代码表类型 | VARCHAR(50) | NOT NULL | CT_Ward |
| CTW_Desc | 科室描述 | VARCHAR(100) | NOT NULL |     |
| CTW_HosCode | 医院编号 | VARCHAR(10) | NOT NULL |     |
| CTW_Remarks | 备注  | VARCHAR(100) |     |     |
| CTW_Status | 状态  | VARCHAR(2) | NOT NULL | 1启用0停用-1删除 |
| CTW_UpdateUserCode | 最后更新人编码 | VARCHAR(10) | NOT NULL |     |
| CTW_UpdateDate | 最后更新日期 | DATE |     | YYYY-MM-DD |
| CTW_UpdateTime | 最后更新时间 | TIME |     | hh:mm:ss |

|     |
| --- |
| **示例** |
| &lt;Request&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;SYS0004&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;CT_WardList&gt;<br><br>&lt;CT_Ward&gt;<br><br>&lt;BusinessFieldCode&gt;00002&lt;/BusinessFieldCode&gt;<br><br>&lt;CTW_Code&gt;PA0052&lt;/CTW_Code&gt;<br><br>&lt;CTW_Desc&gt;麻醉科&lt;/CTW_Desc&gt;<br><br>&lt;CTW_CodesystemCode&gt;CT_Ward&lt;/CTW_CodesystemCode&gt;<br><br>&lt;CTW_Status&gt;1&lt;/CTW_Status&gt;<br><br>&lt;CTW_UpdateUserCode&gt;无&lt;/CTW_UpdateUserCode&gt;<br><br>&lt;CTW_Remarks&gt;无&lt;/CTW_Remarks&gt;<br><br>&lt;CTW_HosCode&gt;PAXRMYY&lt;/CTW_HosCode&gt;<br><br>&lt;/CT_Ward&gt;<br><br>&lt;/CT_WardList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Request&gt; |

应答消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| ResultCode | 响应码 | VARCHAR(6) | NOT NULL | 0：成功 -1:失败 |
| ResultContent | 响应信息 | VARCHAR(300) | NOT NULL |     |
| SuccessIDList | 成功记录id列表 |     |     |     |
| RowID | rowid | VARCHAR(300) |     |     |

|     |
| --- |
| **示例** |
| &lt;Response&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ResultCode&gt;0&lt;/ResultCode&gt;<br><br>&lt;ResultContent&gt;成功&lt;/ResultContent&gt;<br><br>&lt;SuccessIDList&gt;<br><br>&lt;RowID&gt;1&lt;/RowID&gt;<br><br>&lt;RowID&gt;2&lt;/RowID&gt;<br><br>&lt;/SuccessIDList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Response&gt; |

## 6.4发送床位字典信息

|     |     |
| --- | --- |
| **发送床位字典信息** |     |
| 接口方式 | XML+WebService |
| 服务编码 |     |
| 服务名称 | 发送床位字典信息SendManagerDataInfo |
| 服务提供者 | 第三方系统 |
| 服务调用者 | 主数据平台 |
| 调用时机 |     |

请求消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| CTB_BedType | 床位类型 | VARCHAR(50) | NOT NULL |     |
| CTB_Code | 床位代码 | VARCHAR(20) | NOT NULL |     |
| CTB_CodesystemCode | 代码表类型 | VARCHAR(50) | NOT NULL | CT_Bed |
| CTB_Desc | 床位描述 | VARCHAR(100) |     |     |
| CTB_HosCode | 医院编号 | VARCHAR(10) | NOT NULL |     |
| CTB_Remarks | 备注  | VARCHAR (100) | NOT NULL |     |
| CTB_RoomCode | 房间号 | VARCHAR (50) | NOT NULL |     |
| CTB_Status | 状态  | VARCHAR(2) | NOT NULL | 1启用0停用-1删除 |
| CTB_UpdateUserCode | 最后更新人编码 | VARCHAR(20) | NOT NULL |     |
| CTB_UpdateDate | 最后更新日期 | DATE |     | YYYY-MM-DD |
| CTB_UpdateTime | 最后更新时间 | TIME |     | hh:mm:ss |
| CTB_WardCode | 所属病区 | VARCHAR (50) | NOT NULL |     |

|     |
| --- |
| **示例** |
| &lt;Request&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;SYS0004&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;CT_BedList&gt;<br><br>&lt;CT_Bed&gt;<br><br>&lt;BusinessFieldCode&gt;00002&lt;/BusinessFieldCode&gt;<br><br>&lt;CTB_Code&gt;49&lt;/CTB_Code&gt;<br><br>&lt;CTB_Desc&gt;49&lt;/CTB_Desc&gt;<br><br>&lt;CTB_CodesystemCode&gt;CT_Bed&lt;/CTB_CodesystemCode&gt;<br><br>&lt;CTB_Status&gt;1&lt;/CTB_Status&gt;<br><br>&lt;CTB_WardCode&gt;PA0100&lt;/CTB_WardCode&gt;<br><br>&lt;CTB_RoomCode&gt;儿科17&lt;/CTB_RoomCode&gt;<br><br>&lt;CTB_BedType&gt;单人间&lt;/CTB_BedType&gt;<br><br>&lt;CTB_UpdateUserCode&gt;无&lt;/CTB_UpdateUserCode&gt;<br><br>&lt;CTB_Remarks&gt;无&lt;/CTB_Remarks&gt;<br><br>&lt;CTB_HosCode&gt;PAXRMYY&lt;/CTB_HosCode&gt;<br><br>&lt;/CT_Bed&gt;<br><br>&lt;/CT_BedList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Request&gt; |

应答消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| ResultCode | 响应码 | VARCHAR(6) | NOT NULL | 0：成功 -1:失败 |
| ResultContent | 响应信息 | VARCHAR(300) | NOT NULL |     |
| SuccessIDList | 成功记录id列表 |     |     |     |
| RowID | rowid | VARCHAR(300) |     |     |

|     |
| --- |
| **示例** |
| &lt;Response&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ResultCode&gt;0&lt;/ResultCode&gt;<br><br>&lt;ResultContent&gt;成功&lt;/ResultContent&gt;<br><br>&lt;SuccessIDList&gt;<br><br>&lt;RowID&gt;1&lt;/RowID&gt;<br><br>&lt;RowID&gt;2&lt;/RowID&gt;<br><br>&lt;/SuccessIDList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Response&gt; |

## 6.5发送医嘱大类字典信息

|     |     |
| --- | --- |
| **发送医嘱大类字典信息** |     |
| 接口方式 | XML+WebService |
| 服务编码 |     |
| 服务名称 | 发送医嘱大类字典信息SendManagerDataInfo |
| 服务提供者 | 第三方系统 |
| 服务调用者 | 主数据平台 |
| 调用时机 |     |

请求消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| CTC_Code | 代码  | VARCHAR(20) | NOT NULL |     |
| CTC_Desc | 描述  | VARCHAR(100) | NOT NULL |     |
| CTC_CodesystemCode | 代码表类型 | VARCHAR(50) | NOT NULL | CT_Category |
| CTC_Remarks | 备注  | VARCHAR (100) |     |     |
| CTC_Status | 状态  | VARCHAR(2) | NOT NULL | 1启用0停用-1删除 |
| CTC_UpdateUserCode | 最后更新人编码 | VARCHAR(20) | NOT NULL |     |
| CTC_UpdateDate | 最后更新日期 | DATE |     | YYYY-MM-DD |
| CTC_UpdateTime | 最后更新时间 | TIME |     | hh:mm:ss |

|     |
| --- |
| **示例** |
| &lt;Request&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;SYS0004&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;CT_CategoryList&gt;<br><br>&lt;CT_Category&gt;<br><br>&lt;BusinessFieldCode&gt;00002&lt;/BusinessFieldCode&gt;<br><br>&lt;CTC_Code&gt;西药&lt;/CTC_Code&gt;<br><br>&lt;CTC_Desc&gt;西药&lt;/CTC_Desc&gt;<br><br>&lt;CTC_CodesystemCode&gt;CT_Category&lt;/CTC_CodesystemCode&gt;<br><br>&lt;CTC_Status&gt;1&lt;/CTC_Status&gt;<br><br>&lt;CTC_UpdateUserCode&gt;无&lt;/CTC_UpdateUserCode&gt;<br><br>&lt;CTC_UpdateDate&gt;&lt;/CTC_UpdateDate&gt;<br><br>&lt;CTC_UpdateTime&gt;&lt;/CTC_UpdateTime&gt;<br><br>&lt;CTC_Remarks&gt;&lt;/CTC_Remarks&gt;<br><br></CT_Category<br><br>&lt;/CT_CategoryList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Request&gt; |

应答消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| ResultCode | 响应码 | VARCHAR(6) | NOT NULL | 0：成功 -1:失败 |
| ResultContent | 响应信息 | VARCHAR(300) | NOT NULL |     |
| SuccessIDList | 成功记录id列表 |     |     |     |
| RowID | rowid | VARCHAR(300) |     |     |

|     |
| --- |
| **示例** |
| &lt;Response&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ResultCode&gt;0&lt;/ResultCode&gt;<br><br>&lt;ResultContent&gt;成功&lt;/ResultContent&gt;<br><br>&lt;SuccessIDList&gt;<br><br>&lt;RowID&gt;1&lt;/RowID&gt;<br><br>&lt;RowID&gt;2&lt;/RowID&gt;<br><br>&lt;/SuccessIDList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Response&gt; |

## 6.6发送医嘱子类字典信息

|     |     |
| --- | --- |
| **发送医嘱子类字典信息** |     |
| 接口方式 | XML+WebService |
| 服务编码 |     |
| 服务名称 | 发送医嘱子类字典信息SendManagerDataInfo |
| 服务提供者 | 第三方系统 |
| 服务调用者 | 主数据平台 |
| 调用时机 |     |

请求消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| CTCC_Category | 医嘱大类代码 | VARCHAR(20) | NOT NULL |     |
| CTCC_Code | 代码  | VARCHAR(20) | NOT NULL |     |
| CTCC_Desc | 描述  | VARCHAR(100) | NOT NULL |     |
| CTCC_CodesystemCode | 代码表类型 | VARCHAR(50) | NOT NULL | CT_ChildCategory |
| CTCC_Remarks | 备注  | VARCHAR (100) |     |     |
| CTCC_Status | 状态  | VARCHAR(2) | NOT NULL | 1启用0停用-1删除 |
| CTCC_UpdateUserCode | 最后更新人编码 | VARCHAR(20) | NOT NULL |     |
| CTCC_UpdateDate | 最后更新日期 | DATE |     | YYYY-MM-DD |
| CTCC_UpdateTime | 最后更新时间 | TIME |     | hh:mm:ss |

|     |
| --- |
| **示例** |
| &lt;Request&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;SYS0004&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;CT_ChildCategoryList&gt;<br><br>&lt;CT_ChildCategory&gt;<br><br>&lt;BusinessFieldCode&gt;00002&lt;/BusinessFieldCode&gt;<br><br>&lt;CTCC_Code&gt;西药片剂&lt;/CTCC_Code&gt;<br><br>&lt;CTCC_Desc&gt;西药片剂&lt;/CTCC_Desc&gt;<br><br>&lt;CTCC_Category&gt;西药&lt;/CTCC_Category&gt;<br><br>&lt;CTCC_CodesystemCode&gt;CT_ChildCategory&lt;/CTCC_CodesystemCode&gt;<br><br>&lt;CTCC_Status&gt;1&lt;/CTCC_Status&gt;<br><br>&lt;CTCC_UpdateUserCode&gt;无&lt;/CTCC_UpdateUserCode&gt;<br><br>&lt;CTCC_UpdateDate&gt;&lt;/CTCC_UpdateDate&gt;<br><br>&lt;CTCC_UpdateTime&gt;&lt;/CTCC_UpdateTime&gt;<br><br>&lt;CTCC_Remarks&gt;&lt;/CTCC_Remarks&gt;<br><br>&lt;/CT_ChildCategory&gt;<br><br>&lt;/CT_ChildCategoryList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Request&gt; |

应答消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消消息ID | VARCHAR(30) | NOT NULL |     |
| ResultCode | 响响应码 | VARCHAR(6) | NOT NULL | 0：成功 -1:失败 |
| ResultContent | 响响应信息 | VARCHAR(300) | NOT NULL |     |
| SuccessIDList | 成功记录id列表 |     |     |     |
| RowID | rowid | VARCHAR(300) |     |     |

|     |
| --- |
| **示例** |
| &lt;Response&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ResultCode&gt;0&lt;/ResultCode&gt;<br><br>&lt;ResultContent&gt;成功&lt;/ResultContent&gt;<br><br>&lt;SuccessIDList&gt;<br><br>&lt;RowID&gt;1&lt;/RowID&gt;<br><br>&lt;RowID&gt;2&lt;/RowID&gt;<br><br>&lt;/SuccessIDList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Response&gt; |

## 6.7发送医嘱项字典信息

|     |     |
| --- | --- |
| **发送医嘱项字典信息** |     |
| 接口方式 | XML+WebService |
| 服务编码 |     |
| 服务名称 | 发送医嘱项字典信息SendManagerDataInfo |
| 服务提供者 | 第三方系统 |
| 服务调用者 | 主数据平台 |
| 调用时机 |     |

请求消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| CTARCIM_ChildCategory | 医嘱子类代码 | VARCHAR(20) | NOT NULL |     |
| CTARCIM_Code | 医嘱项代码 | VARCHAR(50) | NOT NULL |     |
| CTARCIM_CodesystemCode | 代码表类型 | VARCHAR(50) | NOT NULL | CT_ARCItmMast |
| CTARCIM_Desc | 医嘱项描述 | VARCHAR(300) | NOT NULL |     |
| CTARCIM_Remarks | 备注  | VARCHAR(100) |     |     |
| CTARCIM_Status | 状态  | VARCHAR(2) | NOT NULL | 1启用0停用-1删除 |
| CTARCIM_UpdateUserCode | 最后更新人编码 | VARCHAR(10) | NOT NULL |     |
| CTARCIM_UpdateDate | 最后更新日期 | DATE |     | YYYY-MM-DD |
| CTARCIM_UpdateTime | 最后更新时间 | TIME |     | hh:mm:ss |

|     |
| --- |
| **示例** |
| &lt;Request&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;SYS0004&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;CT_ARCItmMastList&gt;<br><br>&lt;CT_ARCItmMast&gt;<br><br>&lt;BusinessFieldCode&gt;00002&lt;/BusinessFieldCode&gt;<br><br>&lt;CTARCIM_Code&gt;PAY100000003&lt;/CTARCIM_Code&gt;<br><br>&lt;CTARCIM_Desc&gt;阿法骨化醇软胶囊\[0.25ug\*20粒\]&lt;/CTARCIM_Desc&gt;<br><br>&lt;CTARCIM_ChildCategory&gt;西药口服剂&lt;/CTARCIM_ChildCategory&gt;<br><br>&lt;CTARCIM_CodesystemCode&gt;CT_ARCItmMast&lt;/CTARCIM_CodesystemCode&gt;<br><br>&lt;CTARCIM_Status&gt;1&lt;/CTARCIM_Status&gt;<br><br>&lt;CTARCIM_UpdateUserCode&gt;无&lt;/CTARCIM_UpdateUserCode&gt;<br><br>&lt;CTARCIM_UpdateDate&gt;&lt;/CTARCIM_UpdateDate&gt;<br><br>&lt;CTARCIM_UpdateTime&gt;&lt;/CTARCIM_UpdateTime&gt;<br><br>&lt;CTARCIM_Remarks&gt;&lt;/CTARCIM_Remarks&gt;<br><br>&lt;/CT_ARCItmMast&gt;<br><br>&lt;/CT_ARCItmMastList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Request&gt; |

应答消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| ResultCode | 响应码 | VARCHAR(6) | NOT NULL | 0：成功 -1:失败 |
| ResultContent | 响应信息 | VARCHAR(300) | NOT NULL |     |
| SuccessIDList | 成功记录id列表 |     |     |     |
| RowID | rowid | VARCHAR(300) |     |     |

|     |
| --- |
| **示例** |
| &lt;Response&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ResultCode&gt;0&lt;/ResultCode&gt;<br><br>&lt;ResultContent&gt;成功&lt;/ResultContent&gt;<br><br>&lt;SuccessIDList&gt;<br><br>&lt;RowID&gt;1&lt;/RowID&gt;<br><br>&lt;RowID&gt;2&lt;/RowID&gt;<br><br>&lt;/SuccessIDList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Response&gt; |

## 6.8发送医嘱结果状态字典信息

|     |     |
| --- | --- |
| **发送医嘱结果状态字典信息** |     |
| 接口方式 | XML+WebService |
| 服务编码 |     |
| 服务名称 | 发送医嘱结果状态字典信息SendManagerDataInfo |
| 服务提供者 | 第三方系统 |
| 服务调用者 | 主数据平台 |
| 调用时机 |     |

请求消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| CTOS_Code | 代码  | VARCHAR(20) | NOT NULL |     |
| CTOS_Desc | 描述  | VARCHAR(100) | NOT NULL |     |
| CTOS_CodesystemCode | 代码表类型 | VARCHAR(50) | NOT NULL | CT_ResultStatus |
| CTOS_Remarks | 备注  | VARCHAR (100) |     |     |
| CTOS_Status | 状态  | VARCHAR(2) | NOT NULL | 1启用0停用-1删除 |
| CTOS_UpdateUserCode | 最后更新人编码 | VARCHAR(20) | NOT NULL |     |
| CTOS_UpdateDate | 最后更新日期 | DATE |     | YYYY-MM-DD |
| CTOS_UpdateTime | 最后更新时间 | TIME |     | hh:mm:ss |

|     |
| --- |
| **示例** |
| &lt;Request&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;SYS0004&lt;/SourceSystem&gt;<br><br>&lt;MessageID/&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;CT_OrderStatusList&gt;<br><br>&lt;CT_OrderStatus&gt;<br><br>&lt;BusinessFieldCode&gt;00002&lt;/BusinessFieldCode&gt;<br><br>&lt;CTOS_Code&gt;V&lt;/CTOS_Code&gt;<br><br>&lt;CTOS_CodesystemCode&gt;CT_OrderStatus&lt;/CTOS_CodesystemCode&gt;<br><br>&lt;CTOS_Desc&gt;核实&lt;/CTOS_Desc&gt;<br><br>&lt;CTOS_Remarks/&gt;<br><br>&lt;CTOS_Status&gt;1&lt;/CTOS_Status&gt;<br><br>&lt;CTOS_UpdateUserCode&gt;无&lt;/CTOS_UpdateUserCode&gt;<br><br>&lt;/CT_OrderStatus&gt;<br><br>&lt;/CT_OrderStatusList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Request&gt; |

应答消息：

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **代码** | **名称** | **数据类型** | **是否必输** | **备注** |
| SourceSystem | 消息来源 | VARCHAR(50) | NOT NULL |     |
| MessageID | 消息ID | VARCHAR(30) | NOT NULL |     |
| ResultCode | 响应码 | VARCHAR(6) | NOT NULL | 0：成功 -1:失败 |
| ResultContent | 响应信息 | VARCHAR(300) | NOT NULL |     |
| SuccessIDList | 成功记录id列表 |     |     |     |
| RowID | rowid | VARCHAR(300) |     |     |

|     |
| --- |
| **示例** |
| &lt;Response&gt;<br><br>&lt;Header&gt;<br><br>&lt;SourceSystem&gt;&lt;/SourceSystem&gt;<br><br>&lt;MessageID&gt;&lt;/MessageID&gt;<br><br>&lt;/Header&gt;<br><br>&lt;Body&gt;<br><br>&lt;ResultCode&gt;0&lt;/ResultCode&gt;<br><br>&lt;ResultContent&gt;成功&lt;/ResultContent&gt;<br><br>&lt;SuccessIDList&gt;<br><br>&lt;RowID&gt;1&lt;/RowID&gt;<br><br>&lt;RowID&gt;2&lt;/RowID&gt;<br><br>&lt;/SuccessIDList&gt;<br><br>&lt;/Body&gt;<br><br>&lt;/Response&gt; |

# 附录

## 附录一 检查状态字典（绿色部分为本次需要对接的检查状态）

|     |     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- | --- |
| 系统分类代码 | 系统代码 | 系统描述 | 状态代码 | 状态描述 | 顺序  | 服务编码 |
| RIS | PACS | 放射  | AP  | 申请  | 0   |     |
| BK  | 预约  | 1   |     |
| SC  | 登记  | 2   |     |
| IM  | 已有图像 | 3   |     |
| CM  | 检查完成 | 4   |     |
| RP  | 报告  | 5   |     |
| CBK | 取消预约 | \-1 |     |
| CSC | 取消登记 | \-2 |     |
| CIM | 取消图像 | \-3 |     |
| CA  | 取消检查 | \-1 |     |
| CRP | 取消报告 | \-5 |     |
| RD  | 阅读  | 6   |     |

## 附录二 检查文档注册报告内容

|     |
| --- |
| **示例** |
| &lt;?xml version="1.0" encoding="UTF-8"?&gt;<br><br>&lt;clinicalDocument&gt;<br><br>&lt;documentHeader&gt;<br><br>&lt;realmCode&gt;&lt;/realmCode&gt; &lt;!--使用范围--&gt;<br><br>&lt;typeId&gt;&lt;/typeId&gt; &lt;!--文档类型--&gt;<br><br>&lt;template/&gt; &lt;!--模板ID及版本号--&gt;<br><br>&lt;id&gt;&lt;/id&gt; &lt;!--文档流水号--&gt;<br><br>&lt;title&gt;检查报告&lt;/title&gt;<br><br>&lt;effectiveTime&gt;20121024154823&lt;/effectiveTime&gt; &lt;!--文档生成时间--&gt;<br><br>&lt;confidentiality code="级别代码" &gt;级别名称&lt;/confidentiality&gt; &lt;!--保密级别--&gt;<br><br>&lt;versionNumber&gt;&lt;/versionNumber&gt; &lt;!--文档版本号--&gt;<br><br>&lt;author id="医生编号"&gt;医生姓名&lt;/author&gt; &lt;!--文档创作者--&gt;<br><br>&lt;custodian&gt;&lt;/custodian&gt; &lt;!--保管机构--&gt;<br><br>&lt;patient&gt;<br><br>&lt;medicareNo&gt;12797669&lt;/medicareNo&gt; &lt;!--住院号--&gt;<br><br>&lt;admvisitNo&gt;12345678&lt;/admvisitNo&gt; &lt;!--就诊号--&gt;<br><br>&lt;medRecordNo&gt;12797669&lt;/medRecordNo&gt; &lt;!--病案号--&gt;<br><br>&lt;healthCardNo&gt;12345678&lt;/healthCardNo&gt; &lt;!--健康卡号--&gt;<br><br>&lt;certificate&gt;<br><br>&lt;name code="证件类型代码"&gt;证件名称&lt;/name&gt;<br><br>&lt;value&gt;证件号码&lt;/value&gt;<br><br>&lt;/certificate&gt;<br><br>&lt;addr desc="现住址"&gt;<br><br>&lt;text&gt;完整地址&lt;/text&gt;<br><br>&lt;houseNumber&gt;xx号xx小区xx栋xx单元&lt;/houseNumber&gt;<br><br>&lt;streetName&gt;xx大道&lt;/streetName&gt;<br><br>&lt;township&gt;xx乡镇&lt;/township&gt;<br><br>&lt;county&gt;xx区&lt;/county&gt;<br><br>&lt;city&gt;xx市&lt;/city&gt;<br><br>&lt;state&gt;xx省&lt;/state&gt;<br><br>&lt;postalCode&gt;510000&lt;/postalCode&gt;<br><br>&lt;/addr&gt;<br><br>&lt;name&gt;胡传发&lt;/name&gt;<br><br>&lt;telecom&gt;020-87815102&lt;/telecom&gt;<br><br>&lt;administrativeGender code="1" &gt;男性&lt;/administrativeGender&gt;<br><br>&lt;maritalStatus code="20"&gt;已婚&lt;/maritalStatus&gt;<br><br>&lt;ethnicGroup code="01"&gt;汉族&lt;/ethnicGroup&gt;<br><br>&lt;age unit="岁" value="60"/&gt;<br><br>&lt;/patient&gt;<br><br>&lt;participant&gt;<br><br>&lt;code code="1"&gt;家庭关系描述&lt;/code&gt;<br><br>&lt;addr desc="联系人地址"&gt;<br><br>&lt;text&gt;完整地址&lt;/text&gt;<br><br>&lt;houseNumber&gt;xx号&lt;/houseNumber&gt;<br><br>&lt;streetName&gt;xx大道&lt;/streetName&gt;<br><br>&lt;township&gt;xx乡镇&lt;/township&gt;<br><br>&lt;county&gt;xx区&lt;/county&gt;<br><br>&lt;city&gt;xx市&lt;/city&gt;<br><br>&lt;state&gt;xx省&lt;/state&gt;<br><br>&lt;postalCode&gt;02368&lt;/postalCode&gt;<br><br>&lt;/addr&gt;<br><br>&lt;telecom&gt;999-999-999999&lt;/telecom&gt;<br><br>&lt;name&gt;联系人姓名&lt;/name&gt;<br><br>&lt;/participant&gt;<br><br>&lt;/documentHeader&gt;<br><br>&lt;structuredBody&gt;<br><br>&lt;E0004 desc="姓名"&gt;&lt;/E0004&gt;<br><br>&lt;E0005 desc="性别代码"&gt;&lt;/E0005&gt;<br><br>&lt;E0006 desc="性别描述"&gt;&lt;/E0006&gt;<br><br>&lt;E0008 desc="年龄"&gt;&lt;/E0008&gt;<br><br>&lt;E0148 desc="病人类型代码"&gt;&lt;/E0148&gt;<br><br>&lt;E0149 desc="病人类型名称"&gt;&lt;/E0149&gt;<br><br>&lt;E0156 desc="科室id"&gt;&lt;/E0156&gt;<br><br>&lt;E0077 desc="科室"&gt;&lt;/E0077&gt;<br><br>&lt;E0002 desc="住院号"&gt;&lt;/E0002&gt;<br><br>&lt;E0000 desc="门诊号"&gt;&lt;/E0000&gt;<br><br>&lt;E0154 desc="病床id"&gt;&lt;/E0154&gt;<br><br>&lt;E0075 desc="病床"&gt;&lt;/E0075&gt;<br><br>&lt;section code="S0048" desc="诊断"&gt;<br><br>&lt;text/&gt; &lt;!--诊断的详细信息--&gt;<br><br>&lt;E07 desc="医生填写的诊断"&gt;&lt;/E07&gt;<br><br>&lt;E01 desc="诊断名称"&gt;诊断名称&lt;/E01&gt;<br><br>&lt;E02 desc="诊断代码"&gt;诊断代码&lt;/E02&gt;<br><br>&lt;/section&gt;<br><br>&lt;section code="S0076" desc="检查记录"&gt;<br><br>&lt;text/&gt; &lt;!--检查记录的详细信息--&gt;<br><br>&lt;E01 desc="检查号"&gt;&lt;/E01&gt;<br><br>&lt;E02 desc="检查设备编码"&gt;&lt;/E02&gt;<br><br>&lt;E03 desc="检查设备名称"&gt;&lt;/E03&gt;<br><br>&lt;E04 desc="检查部位代码"&gt;&lt;/E04&gt;<br><br>&lt;E05 desc="检查部位名称"&gt;&lt;/E05&gt;<br><br>&lt;E06 desc="检查部位医生手写"&gt;&lt;/E06&gt;<br><br>&lt;E07 desc="检查部位"&gt;&lt;/E07&gt;<br><br>&lt;E08 desc="检查所见"&gt;&lt;/E08&gt;<br><br>&lt;E09 desc="检查结果"&gt;&lt;/E09&gt;<br><br>&lt;E10 desc="检查医师代码"&gt;&lt;/E10&gt;<br><br>&lt;E11 desc="检查医师姓名"&gt;&lt;/E11&gt;<br><br>&lt;E12 desc="审核医师代码"&gt;&lt;/E12&gt;<br><br>&lt;E13 desc="审核医师姓名"&gt;&lt;/E13&gt;<br><br>&lt;E14 desc="记录者代码"&gt;&lt;/E14&gt;<br><br>&lt;E15 desc="记录者姓名"&gt;&lt;/E15&gt;<br><br>&lt;E16 desc="检查日期"&gt;&lt;/E16&gt;<br><br>&lt;E17 desc="检查时间"&gt;&lt;/E17&gt;<br><br>&lt;E18 desc="报告号"&gt;&lt;/E18&gt;<br><br>&lt;E19 desc="审核日期"&gt;&lt;/E19&gt;<br><br>&lt;E20 desc="审核时间"&gt;&lt;/E20&gt;<br><br>&lt;E21 desc="检查项目描述"&gt;&lt;/E21&gt;<br><br>&lt;E22 desc="检查方法描述"&gt;&lt;/E22&gt;<br><br>&lt;E23 desc="报告日期"&gt;&lt;/E23&gt;<br><br>&lt;E24 desc="报告时间"&gt;&lt;/E24&gt;<br><br>&lt;E25 desc="医嘱号"&gt;&lt;/E25&gt;<br><br>&lt;E26 desc="是否危急"&gt;&lt;/E26&gt;<br><br>&lt;E27 desc="是否阳性"&gt;&lt;/E27&gt;<br><br>&lt;E28 desc="异常标记"&gt;&lt;/E28&gt;<br><br>&lt;!--扩展用于检查中的介入性麻醉--&gt;<br><br>&lt;E29 desc="麻醉医师工号"&gt;&lt;/E29&gt;<br><br>&lt;E30 desc="麻醉医师姓名"&gt;&lt;/E30&gt;<br><br>&lt;E31 desc="麻醉方式代码"&gt;&lt;/E31&gt;<br><br>&lt;E32 desc="麻醉方式描述"&gt;&lt;/E32&gt;<br><br>&lt;E33 desc="麻醉分级代码"&gt;&lt;/E33&gt;<br><br>&lt;E34 desc="麻醉分级描述"&gt;&lt;/E34&gt;<br><br>&lt;E35 desc="麻醉开始时间"&gt;&lt;/E35&gt;<br><br>&lt;E36 desc="麻醉结束时间"&gt;&lt;/E36&gt;<br><br>&lt;/section&gt;<br><br>&lt;/structuredBody&gt;<br><br>&lt;/clinicalDocument&gt; |