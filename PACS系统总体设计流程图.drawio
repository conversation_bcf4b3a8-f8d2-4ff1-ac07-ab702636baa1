<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="PACS系统总体设计流程图" id="pacs-system-flow">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- HIS系统 -->
        <mxCell id="his-system" value="HIS" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="480" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- RIS登记 -->
        <mxCell id="ris-register" value="RIS 登记" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="480" y="160" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 数据库服务器 -->
        <mxCell id="database-server" value="数据库服务器" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="480" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 前置服务器 -->
        <mxCell id="front-server" value="前置服务器&lt;br&gt;（数据归档管理）" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="480" y="400" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- 在线存储服务器 -->
        <mxCell id="online-storage" value="在线存储服务器" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="480" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- PACS报告终端 -->
        <mxCell id="pacs-terminal" value="PACS 报告终端" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="160" y="400" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 临床科室 -->
        <mxCell id="clinical-dept" value="临床科室" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="920" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 临床发布 -->
        <mxCell id="clinical-publish" value="临床发布&lt;br&gt;（相关影像数据）" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="920" y="160" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- CT、DR、CR、RF等设备 -->
        <mxCell id="imaging-devices" value="CT,DR,CR,RF 等设备&lt;br&gt;（标准 DICOM）" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="800" y="400" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- 超声设备 -->
        <mxCell id="ultrasound-device" value="超声设备" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="800" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 光盘刻录 -->
        <mxCell id="cd-burn" value="光盘刻录" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="480" y="680" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 连接线和标签 -->
        
        <!-- HIS到RIS登记 -->
        <mxCell id="his-to-ris" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="his-system" target="ris-register">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="120" as="sourcePoint" />
            <mxPoint x="540" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="his-to-ris-label" value="数据接口程序" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="550" y="120" width="80" height="20" as="geometry" />
        </mxCell>
        
        <!-- RIS登记到数据库服务器 -->
        <mxCell id="ris-to-db" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="ris-register" target="database-server">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="240" as="sourcePoint" />
            <mxPoint x="540" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 数据库服务器到前置服务器 -->
        <mxCell id="db-to-front" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="database-server" target="front-server">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="360" as="sourcePoint" />
            <mxPoint x="540" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 前置服务器到在线存储服务器 -->
        <mxCell id="front-to-storage" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="front-server" target="online-storage">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="500" as="sourcePoint" />
            <mxPoint x="540" y="540" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="front-to-storage-label" value="Pack" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="510" y="510" width="40" height="20" as="geometry" />
        </mxCell>
        
        <!-- 在线存储服务器到光盘刻录 -->
        <mxCell id="storage-to-cd" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="online-storage" target="cd-burn">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="620" as="sourcePoint" />
            <mxPoint x="540" y="680" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="storage-to-cd-label" value="Burn" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="510" y="640" width="40" height="20" as="geometry" />
        </mxCell>
        
        <!-- 前置服务器到PACS报告终端 -->
        <mxCell id="front-to-pacs" value="" style="endArrow=classic;html=1;rounded=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="front-server" target="pacs-terminal">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="460" y="440" as="sourcePoint" />
            <mxPoint x="300" y="440" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- PACS报告终端短期数据到前置服务器 -->
        <mxCell id="pacs-to-front-short" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;exitX=1;exitY=0.25;exitDx=0;exitDy=0;" edge="1" parent="1" source="pacs-terminal" target="front-server">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="300" y="415" as="sourcePoint" />
            <mxPoint x="460" y="415" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pacs-short-data-label" value="短期数据" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="340" y="395" width="60" height="20" as="geometry" />
        </mxCell>
        
        <!-- PACS报告终端到在线存储服务器 -->
        <mxCell id="pacs-to-storage" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="pacs-terminal" target="online-storage">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="220" y="480" as="sourcePoint" />
            <mxPoint x="460" y="570" as="targetPoint" />
            <Array as="points">
              <mxPoint x="220" y="570" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="pacs-to-storage-label" value="病人报告" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="300" y="550" width="60" height="20" as="geometry" />
        </mxCell>
        
        <!-- 影像设备到前置服务器 WORKLIST -->
        <mxCell id="devices-to-front-worklist" value="" style="endArrow=classic;html=1;rounded=0;entryX=1;entryY=0.25;entryDx=0;entryDy=0;exitX=0;exitY=0.25;exitDx=0;exitDy=0;" edge="1" parent="1" source="imaging-devices" target="front-server">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="780" y="420" as="sourcePoint" />
            <mxPoint x="620" y="420" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="worklist-label" value="WORKLIST" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="660" y="400" width="80" height="20" as="geometry" />
        </mxCell>
        
        <!-- 前置服务器到影像设备 图像归档程序 -->
        <mxCell id="front-to-devices-archive" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.75;entryDx=0;entryDy=0;exitX=1;exitY=0.75;exitDx=0;exitDy=0;" edge="1" parent="1" source="front-server" target="imaging-devices">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="620" y="460" as="sourcePoint" />
            <mxPoint x="780" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="archive-label" value="图像归档程序" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="650" y="460" width="80" height="20" as="geometry" />
        </mxCell>
        
        <!-- 超声设备到前置服务器 -->
        <mxCell id="ultrasound-to-front" value="" style="endArrow=classic;html=1;rounded=0;entryX=1;entryY=1;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="ultrasound-device" target="front-server">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="780" y="570" as="sourcePoint" />
            <mxPoint x="620" y="480" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 前置服务器到超声设备 -->
        <mxCell id="front-to-ultrasound" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="front-server" target="ultrasound-device">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="620" y="480" as="sourcePoint" />
            <mxPoint x="780" y="570" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ultrasound-connection-label" value="DICOM 方式&lt;br&gt;数据共享&lt;br&gt;采集卡方式" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="650" y="520" width="80" height="50" as="geometry" />
        </mxCell>
        
        <!-- 数据库服务器到临床发布 -->
        <mxCell id="db-to-clinical" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="database-server" target="clinical-publish">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="620" y="310" as="sourcePoint" />
            <mxPoint x="900" y="200" as="targetPoint" />
            <Array as="points">
              <mxPoint x="720" y="310" />
              <mxPoint x="720" y="200" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="db-to-clinical-label" value="通过接件嵌入 HIS 系统" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="730" y="250" width="120" height="30" as="geometry" />
        </mxCell>
        
        <!-- 临床发布到临床科室 -->
        <mxCell id="clinical-to-dept" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="clinical-publish" target="clinical-dept">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="980" y="140" as="sourcePoint" />
            <mxPoint x="980" y="120" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="clinical-webserver-label" value="Webserver" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1000" y="120" width="80" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
