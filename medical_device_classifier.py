#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗设备分类器
根据医嘱名称智能识别设备类型和检查部位
"""

import pandas as pd
import re
from typing import Tuple, List, Dict, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MedicalDeviceClassifier:
    """医疗设备智能分类器"""
    
    def __init__(self):
        """初始化分类器"""
        self.device_type_rules = {
            'DR': ['DR', '数字化X线', 'X线摄影', '胸部正位', '胸部侧位', '胸片', '头颅正位', '头颅侧位', 
                   '颈椎', '腰椎', '下颌骨', '颧弓', '茎突', '髁突', '华氏位'],
            'CR': ['CR', '计算机X线', '牙片', '咬合片', '咬合翼片'],
            'CT': ['CT', '计算机体层', '锥形束', 'CBCT', '断层扫描', '体层成像'],
            'MR': ['磁共振', 'MRI', 'MRA', 'CGZ'],
            'US': ['超声', '多普勒', 'CSDPLCS'],
            'NM': ['显像', '核医学', 'SPECT', '单光子'],
            'ECG': ['心电图', 'ECG', 'XDT'],
            'DSA': ['造影', '血管造影', 'ZY'],
            'C型臂': ['C型臂', '透视', 'CXBSZTS'],
            '病理科': ['病理', '活检', '切片', '诊断', '染色', '显微', '组织', '标本', 
                     '局部切除', '手术标本', '冰冻', '石蜡', '免疫', '会诊']
        }
        
        self.examination_type_rules = {
            '头颈部': ['头颅', '颅', '颞下颌', '下颌骨', '茎突', '髁突', '颧弓', '华氏位', 'TL', 'XHG', 'JT', 'KT', 'QG', 'HS'],
            '胸部': ['胸部', '胸片', '胸', 'XB'],
            '脊柱': ['颈椎', '腰椎', '脊柱', 'JZ', 'YZ'],
            '口腔颌面': ['牙', '牙列', '牙体', '牙髓', '口腔', '全景', '咬合', '唾液腺', 'YC', 'YL', 'YT', 'YS', 
                       'QJP', 'QMTC', 'YH', 'TYX', 'HMZ', 'LHMZ'],
            '关节': ['关节', '颞下颌关节', 'GJ', 'NXHGJ'],
            '血管': ['血管', '造影', 'XG', 'ZY'],
            '软组织': ['组织', '活检', '切除', 'ZZ', 'HJ', 'QC'],
            '心脏': ['心电', '心脏', 'XD'],
            '消化系统': ['消化', '内镜', 'XH', 'NJ'],
            '病理': ['病理', '活检', '组织', '标本', '切片', 'BL', 'HJ', 'ZZ', 'BB', 'QP']
        }
        
    def _extract_keywords(self, text: str) -> List[str]:
        """提取文本中的关键词"""
        if not text:
            return []
        
        # 移除括号内容和特殊符号，保留中英文和数字
        cleaned_text = re.sub(r'[（(].*?[）)]', '', text)
        cleaned_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9+\-]', ' ', cleaned_text)
        
        # 分割成词
        keywords = cleaned_text.split()
        keywords.extend(re.findall(r'[\u4e00-\u9fa5]+', text))  # 提取中文词
        keywords.extend(re.findall(r'[A-Z]+', text))  # 提取大写字母缩写
        
        return list(set(keywords))
    
    def identify_device_type(self, order_name: str) -> Tuple[Optional[str], float]:
        """
        识别设备类型
        
        Args:
            order_name: 医嘱名称
            
        Returns:
            Tuple[设备类型, 置信度]
        """
        if not order_name:
            return None, 0.0
            
        keywords = self._extract_keywords(order_name)
        logger.debug(f"提取的关键词: {keywords}")
        
        device_scores = {}
        
        # 计算每种设备类型的匹配得分
        for device_type, patterns in self.device_type_rules.items():
            score = 0
            for pattern in patterns:
                if pattern in order_name:
                    # 精确匹配得分更高
                    if pattern == order_name.strip():
                        score += 10
                    elif len(pattern) > 2:  # 长关键词权重更高
                        score += 3
                    else:
                        score += 1
                        
                # 检查关键词匹配
                for keyword in keywords:
                    if pattern.upper() in keyword.upper() or keyword.upper() in pattern.upper():
                        score += 2
                        
            if score > 0:
                device_scores[device_type] = score
                
        if not device_scores:
            return None, 0.0
            
        # 选择得分最高的设备类型
        best_device = max(device_scores, key=device_scores.get)
        max_score = device_scores[best_device]
        
        # 计算置信度 (0-1)
        confidence = min(max_score / 10.0, 1.0)
        
        logger.info(f"设备识别: '{order_name}' -> {best_device} (置信度: {confidence:.2f})")
        return best_device, confidence
    
    def identify_examination_type(self, order_name: str) -> Tuple[Optional[str], float]:
        """
        识别检查项目类型(部位)
        
        Args:
            order_name: 医嘱名称
            
        Returns:
            Tuple[检查部位, 置信度]
        """
        if not order_name:
            return None, 0.0
            
        keywords = self._extract_keywords(order_name)
        type_scores = {}
        
        # 计算每种检查类型的匹配得分
        for exam_type, patterns in self.examination_type_rules.items():
            score = 0
            for pattern in patterns:
                if pattern in order_name:
                    # 精确匹配得分更高
                    if len(pattern) > 2:
                        score += 3
                    else:
                        score += 1
                        
                # 检查关键词匹配
                for keyword in keywords:
                    if pattern.upper() in keyword.upper() or keyword.upper() in pattern.upper():
                        score += 2
                        
            if score > 0:
                type_scores[exam_type] = score
                
        if not type_scores:
            return None, 0.0
            
        # 选择得分最高的检查类型
        best_type = max(type_scores, key=type_scores.get)
        max_score = type_scores[best_type]
        
        # 计算置信度
        confidence = min(max_score / 8.0, 1.0)
        
        logger.info(f"部位识别: '{order_name}' -> {best_type} (置信度: {confidence:.2f})")
        return best_type, confidence
    
    def classify_medical_order(self, order_name: str) -> Dict:
        """
        对单个医嘱进行完整分类
        
        Args:
            order_name: 医嘱名称
            
        Returns:
            分类结果字典
        """
        device_type, device_confidence = self.identify_device_type(order_name)
        exam_type, exam_confidence = self.identify_examination_type(order_name)
        
        result = {
            'order_name': order_name,
            'device_type': device_type,
            'device_confidence': device_confidence,
            'examination_type': exam_type,
            'examination_confidence': exam_confidence,
            'needs_review': device_confidence < 0.7 or exam_confidence < 0.7
        }
        
        return result
    
    def process_csv_file(self, file_path: str, output_path: str = None) -> pd.DataFrame:
        """
        处理整个CSV文件
        
        Args:
            file_path: 输入CSV文件路径
            output_path: 输出文件路径，如不指定则覆盖原文件
            
        Returns:
            处理后的DataFrame
        """
        logger.info(f"开始处理文件: {file_path}")
        
        # 读取CSV文件
        try:
            df = pd.read_csv(file_path, encoding='utf-8')
        except UnicodeDecodeError:
            df = pd.read_csv(file_path, encoding='gbk')
            
        logger.info(f"共读取 {len(df)} 行数据")
        
        # 获取列名
        device_col = '设备类型'
        exam_col = '检查项目类型（部位）'
        order_col = '医嘱名称'
        
        if order_col not in df.columns:
            raise ValueError(f"未找到列: {order_col}")
            
        # 初始化结果列表
        results = []
        
        # 逐行处理
        for index, row in df.iterrows():
            order_name = str(row[order_col]) if pd.notna(row[order_col]) else ""
            current_device = row[device_col] if device_col in df.columns and pd.notna(row[device_col]) else ""
            current_exam = row[exam_col] if exam_col in df.columns and pd.notna(row[exam_col]) else ""
            
            # 只对缺失数据进行分类
            if not current_device or not current_exam:
                classification = self.classify_medical_order(order_name)
                
                # 更新设备类型（如果原来为空）
                if not current_device and classification['device_type']:
                    df.at[index, device_col] = classification['device_type']
                    
                # 更新检查类型（如果原来为空）
                if not current_exam and classification['examination_type']:
                    df.at[index, exam_col] = classification['examination_type']
                    
                results.append(classification)
            else:
                # 记录已有分类
                results.append({
                    'order_name': order_name,
                    'device_type': current_device,
                    'device_confidence': 1.0,
                    'examination_type': current_exam,
                    'examination_confidence': 1.0,
                    'needs_review': False
                })
        
        # 保存结果
        output_file = output_path or file_path
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        logger.info(f"结果已保存到: {output_file}")
        
        # 生成分类报告
        self._generate_classification_report(results, file_path.replace('.csv', '_classification_report.txt'))
        
        return df
    
    def _generate_classification_report(self, results: List[Dict], report_path: str):
        """生成分类报告"""
        total_items = len(results)
        classified_items = sum(1 for r in results if r['device_type'] and r['examination_type'])
        needs_review = sum(1 for r in results if r['needs_review'])
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("医疗设备分类报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"总项目数: {total_items}\n")
            f.write(f"已分类项目数: {classified_items}\n")
            f.write(f"分类完成率: {classified_items/total_items*100:.1f}%\n")
            f.write(f"需要人工复核项目数: {needs_review}\n\n")
            
            f.write("需要复核的项目:\n")
            f.write("-" * 30 + "\n")
            for r in results:
                if r['needs_review']:
                    f.write(f"医嘱名称: {r['order_name']}\n")
                    f.write(f"设备类型: {r['device_type']} (置信度: {r['device_confidence']:.2f})\n")
                    f.write(f"检查部位: {r['examination_type']} (置信度: {r['examination_confidence']:.2f})\n")
                    f.write("-" * 30 + "\n")
        
        logger.info(f"分类报告已生成: {report_path}")

def main():
    """主函数"""
    classifier = MedicalDeviceClassifier()
    
    # 处理CSV文件
    input_file = "检查项目.csv"
    try:
        result_df = classifier.process_csv_file(input_file)
        print(f"处理完成！共处理 {len(result_df)} 条记录")
        print("详细报告请查看生成的 *_classification_report.txt 文件")
    except Exception as e:
        logger.error(f"处理过程中出现错误: {e}")
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
